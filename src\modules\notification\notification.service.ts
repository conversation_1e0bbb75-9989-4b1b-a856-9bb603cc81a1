import { HttpService } from "@nestjs/axios";
import { Injectable, Logger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { CategoryType } from "@prisma/client";
import { firstValueFrom } from "rxjs";
import { CategoryPayload } from "../category/types/category.types";
import { TicketPayload } from "../ticket/types/ticket.types";

interface WebhookPayload {
  event: "ticket.created" | "ticket.escalated";
  ticket: TicketPayload;
  category: CategoryPayload;
  escalationTarget?: string;
}

interface EmailPayload {
  subject: string;
  body: string;
  to: string[];
  cc?: string[];
  bcc?: string[];
}

interface EmailConfig {
  apiUrl: string;
  recipients?: string[];
  teamDistributionList?: string;
  escalationDistributionList?: string;
}

@Injectable()
export class NotificationService {
  private readonly logger = new Logger(NotificationService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
  ) {}

  async sendTicketNotification(ticket: TicketPayload, category: CategoryPayload): Promise<void> {
    this.logger.debug(`Sending notification for ticket ${ticket.id}`);

    try {
      switch (category.type) {
        case CategoryType.partner:
          await this.notifyPartner(ticket, category);
          break;
        case CategoryType.software_partner:
          await this.notifySoftwarePartner(ticket, category);
          break;
        case CategoryType.internal:
          await this.notifyInternal(ticket, category);
          break;
      }

      this.logger.debug(`Successfully sent notification for ticket ${ticket.id}`);
    } catch (error) {
      this.logger.error(`Error sending notification for ticket ${ticket.id}:`, error);
      // Don't throw - we don't want notification failures to break ticket creation
    }
  }

  private async notifyPartner(ticket: TicketPayload, category: CategoryPayload): Promise<void> {
    const webhookUrl = this.configService.get<string>("PARTNER_WEBHOOK_URL");
    if (!webhookUrl) {
      this.logger.warn("PARTNER_WEBHOOK_URL not configured");
      return;
    }

    const payload: WebhookPayload = {
      event: "ticket.created",
      ticket,
      category
    };

    try {
      await firstValueFrom(this.httpService.post(webhookUrl, payload));
      this.logger.debug(`Successfully sent partner webhook for ticket ${ticket.id}`);
    } catch (error) {
      this.logger.error(`Error sending partner webhook for ticket ${ticket.id}:`, error);
    }
  }

  private async notifySoftwarePartner(ticket: TicketPayload, category: CategoryPayload): Promise<void> {
    const webhookUrl = this.configService.get<string>("SOFTWARE_PARTNER_WEBHOOK_URL");
    const emailConfig = this.configService.get<EmailConfig>("SOFTWARE_PARTNER_EMAIL_CONFIG");

    if (webhookUrl) {
      const payload: WebhookPayload = {
        event: "ticket.created",
        ticket,
        category
      };

      try {
        await firstValueFrom(this.httpService.post(webhookUrl, payload));
        this.logger.debug(`Successfully sent software partner webhook for ticket ${ticket.id}`);
      } catch (error) {
        this.logger.error(`Error sending software partner webhook for ticket ${ticket.id}:`, error);
      }
    }

    if (emailConfig?.apiUrl && emailConfig.recipients?.length) {
      const emailPayload: EmailPayload = {
        subject: `New Support Ticket: ${ticket.subject}`,
        body: this.generateTicketEmailBody(ticket, category),
        to: emailConfig.recipients
      };

      try {
        await firstValueFrom(this.httpService.post(emailConfig.apiUrl, emailPayload));
        this.logger.debug(`Successfully sent software partner email for ticket ${ticket.id}`);
      } catch (error) {
        this.logger.error(`Error sending software partner email for ticket ${ticket.id}:`, error);
      }
    }
  }

  private async notifyInternal(ticket: TicketPayload, category: CategoryPayload): Promise<void> {
    const emailConfig = this.configService.get<EmailConfig>("INTERNAL_EMAIL_CONFIG");
    if (!emailConfig?.apiUrl) {
      this.logger.warn("INTERNAL_EMAIL_CONFIG not configured");
      return;
    }

    const emailPayload: EmailPayload = {
      subject: `New Internal Support Ticket: ${ticket.subject}`,
      body: this.generateTicketEmailBody(ticket, category),
      to: [...category.autoAssignTo || []],
      cc: emailConfig.teamDistributionList ? [emailConfig.teamDistributionList] : undefined
    };

    try {
      await firstValueFrom(this.httpService.post(emailConfig.apiUrl, emailPayload));
      this.logger.debug(`Successfully sent internal email for ticket ${ticket.id}`);
    } catch (error) {
      this.logger.error(`Error sending internal email for ticket ${ticket.id}:`, error);
    }
  }

  async sendEscalationNotification(ticket: TicketPayload, category: CategoryPayload): Promise<void> {
    this.logger.debug(`Sending escalation notification for ticket ${ticket.id}`);

    try {
      const escalationTarget = category.escalateTo;
      if (!escalationTarget) {
        this.logger.warn(`No escalation target configured for category ${category.id}`);
        return;
      }

      // Send escalation notification based on category type
      switch (category.type) {
        case CategoryType.partner:
          await this.sendPartnerEscalation(ticket, category, escalationTarget);
          break;
        case CategoryType.software_partner:
          await this.sendSoftwarePartnerEscalation(ticket, category, escalationTarget);
          break;
        case CategoryType.internal:
          await this.sendInternalEscalation(ticket, category, escalationTarget);
          break;
      }

      this.logger.debug(`Successfully sent escalation notification for ticket ${ticket.id}`);
    } catch (error) {
      this.logger.error(`Error sending escalation notification for ticket ${ticket.id}:`, error);
    }
  }

  private async sendPartnerEscalation(
    ticket: TicketPayload, 
    category: CategoryPayload,
    escalationTarget: string
  ): Promise<void> {
    const webhookUrl = this.configService.get<string>("PARTNER_WEBHOOK_URL");
    const emailConfig = this.configService.get<EmailConfig>("PARTNER_EMAIL_CONFIG");

    if (webhookUrl) {
      const payload: WebhookPayload = {
        event: "ticket.escalated",
        ticket,
        category,
        escalationTarget
      };

      try {
        await firstValueFrom(this.httpService.post(webhookUrl, payload));
      } catch (error) {
        this.logger.error(`Error sending partner escalation webhook for ticket ${ticket.id}:`, error);
      }
    }

    if (emailConfig?.apiUrl) {
      const emailPayload: EmailPayload = {
        subject: `ESCALATED: Support Ticket ${ticket.subject}`,
        body: this.generateEscalationEmailBody(ticket, category, escalationTarget),
        to: [escalationTarget],
        cc: emailConfig.escalationDistributionList ? [emailConfig.escalationDistributionList] : undefined
      };

      try {
        await firstValueFrom(this.httpService.post(emailConfig.apiUrl, emailPayload));
      } catch (error) {
        this.logger.error(`Error sending partner escalation email for ticket ${ticket.id}:`, error);
      }
    }
  }

  private async sendSoftwarePartnerEscalation(
    ticket: TicketPayload, 
    category: CategoryPayload,
    escalationTarget: string
  ): Promise<void> {
    const webhookUrl = this.configService.get<string>("SOFTWARE_PARTNER_WEBHOOK_URL");
    const emailConfig = this.configService.get<EmailConfig>("SOFTWARE_PARTNER_EMAIL_CONFIG");

    if (webhookUrl) {
      const payload: WebhookPayload = {
        event: "ticket.escalated",
        ticket,
        category,
        escalationTarget
      };

      try {
        await firstValueFrom(this.httpService.post(webhookUrl, payload));
      } catch (error) {
        this.logger.error(`Error sending software partner escalation webhook for ticket ${ticket.id}:`, error);
      }
    }

    if (emailConfig?.apiUrl) {
      const emailPayload: EmailPayload = {
        subject: `ESCALATED: Support Ticket ${ticket.subject}`,
        body: this.generateEscalationEmailBody(ticket, category, escalationTarget),
        to: [escalationTarget],
        cc: emailConfig.escalationDistributionList ? [emailConfig.escalationDistributionList] : undefined
      };

      try {
        await firstValueFrom(this.httpService.post(emailConfig.apiUrl, emailPayload));
      } catch (error) {
        this.logger.error(`Error sending software partner escalation email for ticket ${ticket.id}:`, error);
      }
    }
  }

  private async sendInternalEscalation(
    ticket: TicketPayload, 
    category: CategoryPayload,
    escalationTarget: string
  ): Promise<void> {
    const emailConfig = this.configService.get<EmailConfig>("INTERNAL_EMAIL_CONFIG");
    if (!emailConfig?.apiUrl) {
      this.logger.warn("INTERNAL_EMAIL_CONFIG not configured");
      return;
    }

    const emailPayload: EmailPayload = {
      subject: `ESCALATED: Internal Support Ticket ${ticket.subject}`,
      body: this.generateEscalationEmailBody(ticket, category, escalationTarget),
      to: [escalationTarget],
      cc: emailConfig.escalationDistributionList ? [emailConfig.escalationDistributionList] : undefined
    };

    try {
      await firstValueFrom(this.httpService.post(emailConfig.apiUrl, emailPayload));
    } catch (error) {
      this.logger.error(`Error sending internal escalation email for ticket ${ticket.id}:`, error);
    }
  }

  private generateTicketEmailBody(ticket: TicketPayload, category: CategoryPayload): string {
    return `
A new support ticket has been created:

Subject: ${ticket.subject}
Priority: ${ticket.priority}
Category: ${category.name}

Description:
${ticket.description}

Assigned To: ${ticket.assignedTo.join(", ") || "Unassigned"}
Created By: ${ticket.createdBy}

View ticket: ${this.configService.get("APP_URL")}/tickets/${ticket.id}
`;
  }

  private generateEscalationEmailBody(
    ticket: TicketPayload, 
    category: CategoryPayload,
    escalationTarget: string
  ): string {
    return `
A support ticket has been escalated:

Subject: ${ticket.subject}
Priority: ${ticket.priority}
Category: ${category.name}

Description:
${ticket.description}

Original Assignees: ${ticket.assignedTo.join(", ") || "Unassigned"}
Escalated To: ${escalationTarget}
Created By: ${ticket.createdBy}
Created At: ${ticket.createdAt.toISOString()}
Last Updated: ${ticket.updatedAt.toISOString()}

Reason for Escalation:
- Ticket has exceeded the response time threshold of ${category.timeoutMinutes} minutes

View ticket: ${this.configService.get("APP_URL")}/tickets/${ticket.id}
`;
  }
}
