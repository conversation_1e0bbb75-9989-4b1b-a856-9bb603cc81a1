import { Resolver, Query, Mutation, Args, ID } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { CategoryService } from './category.service';
import { Category } from './category.model';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { GraphQLAuthGuard } from '../auth/src/guards';
import { ScopeGuard, SupportAccess, GlobalAdminOnly } from '../auth/src/guards/scope.guard';

@Resolver(() => Category)
@UseGuards(GraphQLAuthGuard, ScopeGuard)
export class CategoryResolver {
  constructor(private readonly categoryService: CategoryService) {}

  @Query(() => [Category], { name: 'categories' })
  @SupportAccess()
  async findAll() {
    return this.categoryService.findMany({});
  }

  @Query(() => Category, { name: 'category' })
  @SupportAccess()
  async findOne(@Args('id', { type: () => ID }) id: string) {
    return this.categoryService.findUnique({ id });
  }

  @Mutation(() => Category)
  @GlobalAdminOnly()
  async createCategory(@Args('data') data: CreateCategoryDto) {
    return this.categoryService.create(data);
  }

  @Mutation(() => Category)
  @GlobalAdminOnly()
  async updateCategory(
    @Args('id', { type: () => ID }) id: string,
    @Args('data') data: UpdateCategoryDto,
  ) {
    return this.categoryService.update(id, data);
  }

  @Mutation(() => Category)
  @GlobalAdminOnly()
  async deleteCategory(@Args('id', { type: () => ID }) id: string) {
    return this.categoryService.delete(id);
  }
}
