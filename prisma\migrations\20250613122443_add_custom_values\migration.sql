-- CreateEnum
CREATE TYPE "CustomValueType" AS ENUM ('NUMBER', 'STRING', 'JSON', 'BOOLEAN');

-- CreateTable
CREATE TABLE "CustomValue" (
    "id" TEXT NOT NULL,
    "key" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "value" TEXT NOT NULL,
    "type" "CustomValueType" NOT NULL,
    "category" TEXT NOT NULL,
    "isSystem" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "createdBy" TEXT NOT NULL,
    "lastUpdatedBy" TEXT NOT NULL,

    CONSTRAINT "CustomValue_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "CustomValue_key_key" ON "CustomValue"("key");
