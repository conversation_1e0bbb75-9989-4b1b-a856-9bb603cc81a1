import { Module } from "@nestjs/common";
import { PrismaModule } from "../../prisma/prisma.module";
import { CustomValueController } from "./custom-value.controller";
import { CustomValueService } from "./custom-value.service";
import { CustomValueResolver } from './custom-value.resolver';
import { AuthModule } from "../auth/src/auth.module";

@Module({
  imports: [PrismaModule, AuthModule],
  controllers: [CustomValueController],
  providers: [CustomValueService, CustomValueResolver],
  exports: [CustomValueService]
})
export class CustomValueModule {}
