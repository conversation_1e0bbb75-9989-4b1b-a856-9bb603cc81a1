import { Prisma } from "@prisma/client";

export const commentSelect = {
  id: true,
  message: true,
  authorId: true,
  ticketId: true,
  createdAt: true,
  updatedAt: true,
  // User data fields fetched from auth API (same as ticket system)
  firstName: true,
  lastName: true,
  email: true,
  // Partner-related fields (same as ticket system)
  partnerUserId: true,
  partnerRole: true,
  partnerOrgId: true,
  entSet: true,
  // User display fields
  authorName: true,
  authorEmail: true,
} as const;

export type CommentSelect = typeof commentSelect;

export type CommentPayload = Prisma.CommentGetPayload<{
  select: CommentSelect;
}>;

export interface CreateCommentInput {
  message: string;
  authorId: string;
  ticketId: string;
}

export interface FindManyCommentsArgs {
  where?: Prisma.CommentWhereInput;
  orderBy?: Prisma.CommentOrderByWithRelationInput;
  skip?: number;
  take?: number;
}
