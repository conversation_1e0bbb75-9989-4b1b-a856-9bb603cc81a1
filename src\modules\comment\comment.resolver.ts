import { Resolver, Query, Mutation, Args, ID, Context } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { CommentService } from './comment.service';
import { Comment } from './comment.model';
import { CreateCommentDto } from './dto/create-comment.dto';
import { GraphQLAuthGuard } from '../auth/src/guards';
import { ScopeGuard, SupportAccess } from '../auth/src/guards/scope.guard';

@Resolver(() => Comment)
@UseGuards(GraphQLAuthGuard, ScopeGuard)
export class CommentResolver {
  constructor(private readonly commentService: CommentService) {}

  @Query(() => [Comment], { name: 'commentsByTicket' })
  @SupportAccess()
  async commentsByTicket(@Args('ticketId', { type: () => String }) ticketId: string) {
    return this.commentService.findByTicketId(ticketId);
  }

  @Mutation(() => Comment)
  @SupportAccess()
  async createComment(
    @Args('data') data: CreateCommentDto,
    @Context() context: any
  ) {
    const user = context.req.user;
    console.log('createComment input data:', data, JSON.stringify(data));
    return this.commentService.create({
      message: data.message,
      authorId: user.id,
      ticket: { connect: { id: data.ticketId } }
    });
  }

  @Mutation(() => Comment)
  @UseGuards(GraphQLAuthGuard)
  async deleteComment(@Args('id', { type: () => ID }) id: string) {
    return this.commentService.delete({ id });
  }
}
