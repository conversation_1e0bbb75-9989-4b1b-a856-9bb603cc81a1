import { Resolver, Query, Mutation, Args, Context } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { CustomValueService } from './custom-value.service';
import { CustomValue } from './custom-value.model';
import { CreateCustomValueDto, UpdateCustomValueDto } from './dto/custom-value.dto';
import { GraphQLAuthGuard } from '../auth/src/guards';
import { ScopeGuard, SupportAccess, GlobalAdminOnly } from '../auth/src/guards/scope.guard';

@Resolver(() => CustomValue)
@UseGuards(GraphQLAuthGuard, ScopeGuard)
export class CustomValueResolver {
  constructor(private readonly customValueService: CustomValueService) {}

  @Query(() => [CustomValue], { name: 'customValues' })
  @SupportAccess()
  async findAll(@Args('category', { type: () => String, nullable: true }) category?: string) {
    return this.customValueService.findAll(category);
  }

  @Query(() => CustomValue, { name: 'customValue' })
  @SupportAccess()
  async findByKey(@Args('key', { type: () => String }) key: string) {
    return this.customValueService.findByKey(key);
  }

  @Mutation(() => CustomValue)
  @GlobalAdminOnly()
  async createCustomValue(
    @Args('data') data: CreateCustomValueDto,
    @Context() context: any
  ) {
    const user = context.req.user;
    console.log('createCustomValue input data:', data, 'user:', user?.id);
    return this.customValueService.create(data, user.id);
  }

  @Mutation(() => CustomValue)
  @GlobalAdminOnly()
  async updateCustomValue(
    @Args('key', { type: () => String }) key: string,
    @Args('data') data: UpdateCustomValueDto,
    @Context() context: any
  ) {
    const user = context.req.user;
    console.log('updateCustomValue input data:', { key, data }, 'user:', user?.id);
    return this.customValueService.update(key, data, user.id);
  }

  @Mutation(() => CustomValue)
  @UseGuards(GraphQLAuthGuard)
  async deleteCustomValue(@Args('key', { type: () => String }) key: string) {
    return this.customValueService.delete(key);
  }
}
