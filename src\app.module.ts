import { HttpModule } from "@nestjs/axios";
import { Module } from "@nestjs/common";
import { AppController } from "./app.controller";
import { ConfigModule } from "./config/config.module";
import { AuthModule } from "./modules/auth/src/auth.module";
import { WebhookModule } from "./webhook/webhook.module";
import { FileModule } from "./modules/backblaze/file.module";
import { CategoryModule } from "./modules/category/category.module";
import { CommentModule } from "./modules/comment/comment.module";
import { CustomValueModule } from "./modules/custom-value/custom-value.module";
import { EscalationModule } from "./modules/escalation/escalation.module";
import { NotificationModule } from "./modules/notification/notification.module";
import { TicketModule } from "./modules/ticket/ticket.module";

import { HealthModule } from "./modules/health/health.module";
import { GraphQLModule } from "@nestjs/graphql";
import { ApolloDriver, ApolloDriverConfig } from "@nestjs/apollo";
import { join } from 'path';
import { FastifyRequest, FastifyReply } from 'fastify';
import { GraphQLUpload } from 'graphql-upload-minimal';

@Module({
  imports: [
    GraphQLModule.forRoot<ApolloDriverConfig>({
      driver: ApolloDriver,
      autoSchemaFile: join(process.cwd(), 'schema.gql'),
      sortSchema: true,
      playground: {
        settings: {
          'request.credentials': 'include',
        },
      },
      introspection: true, // Enable introspection for playground
      context: ({ req, reply }: { req: FastifyRequest; reply: FastifyReply }) => ({ req, reply }),
      resolvers: {
        Upload: GraphQLUpload,
      },
      csrfPrevention: false, // Disable CSRF for file uploads
    }),
    ConfigModule,
    WebhookModule,
    AuthModule, // New authentication module
    HttpModule,
    FileModule,
    HealthModule,
    CategoryModule,
    TicketModule,
    CommentModule,
    CustomValueModule,
    EscalationModule,
    NotificationModule,
  ],
  controllers: [AppController],
  providers: [],
})
export class AppModule {}
