# ------------------------------------------------------
# THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
# ------------------------------------------------------

type Category {
  autoAssignTo: [String!]!
  createdAt: DateTime!
  description: String
  escalateTo: String
  id: String!
  name: String!
  timeoutMinutes: Float!
  type: String!
  updatedAt: DateTime!
}

"""The type of the category (internal, partner, software_partner)"""
enum CategoryType {
  internal
  partner
  software_partner
}

type Comment {
  authorId: String!
  createdAt: DateTime!
  files: [SupportFile!]
  id: String!
  message: String!
  ticketId: String!
  updatedAt: DateTime!
}

input CreateCategoryDto {
  autoAssignTo: [String]
  description: String
  escalateTo: String
  name: String!
  timeoutMinutes: Float = 60
  type: CategoryType
}

input CreateCommentDto {
  message: String!
  ticketId: String!
}

input CreateCustomValueDto {
  category: String!
  description: String
  isSystem: Boolean
  key: String!
  name: String!
  type: CustomValueType!
  value: String!
}

input CreateTicketInput {
  accountId: String
  assignedTo: [String!]
  categoryId: String!
  description: String!
  partnerId: String
  priority: TicketPriority!
  subject: String!
}

type CurrentUser {
  accountId: String
  active: Boolean!
  country: String
  createdAt: DateTime!
  email: String!
  firstName: String!
  id: ID!
  lastName: String!
  mfaEnabled: Boolean!
  partnerId: [String!]!
  permissions: [String!]
  phone: String
  role: String!
  verifiedEmail: Boolean!
  verifiedPhone: Boolean!
}

type CustomValue {
  category: String!
  createdAt: DateTime!
  createdBy: String!
  description: String
  isSystem: Boolean
  key: String!
  lastUpdatedBy: String!
  name: String!
  type: String!
  updatedAt: DateTime!
  value: String!
}

"""The type of the custom value for validation and parsing"""
enum CustomValueType {
  BOOLEAN
  JSON
  NUMBER
  STRING
}

"""
A date-time string at UTC, such as 2019-12-03T09:54:33Z, compliant with the date-time format.
"""
scalar DateTime

input GetUsersDto {
  active: Boolean
  limit: Float = 10
  page: Float = 1
  role: String
  search: String
}

type Mutation {
  createCategory(data: CreateCategoryDto!): Category!
  createComment(data: CreateCommentDto!): Comment!
  createCustomValue(data: CreateCustomValueDto!): CustomValue!
  createTicket(data: CreateTicketInput!): Ticket!
  deleteCategory(id: ID!): Category!
  deleteComment(id: ID!): Comment!
  deleteCustomValue(key: String!): CustomValue!
  deleteSupportFile(id: String!): SupportFile!
  deleteTicket(id: String!): Ticket!
  updateCategory(data: UpdateCategoryDto!, id: ID!): Category!
  updateCustomValue(data: UpdateCustomValueDto!, key: String!): CustomValue!
  updateTicket(data: UpdateTicketDto!, id: String!): Ticket!
  uploadSupportFile(commentId: String, file: Upload!, ticketId: String): SupportFile!
}

type Query {
  activeUsers: [User!]!
  authPing: String!
  categories: [Category!]!
  category(id: ID!): Category!
  commentsByTicket(ticketId: String!): [Comment!]!
  customValue(key: String!): CustomValue!
  customValues(category: String): [CustomValue!]!
  me: CurrentUser!
  supportFile(id: String!): SupportFile!
  supportFiles(commentId: String, ticketId: String): [SupportFile!]!
  ticket(id: String!): Ticket!
  tickets: [Ticket!]!
  user(id: String!): User!
  users(params: GetUsersDto): UserListResponse!
  usersByRole(role: String!): [User!]!
  validateToken(token: String!): String!
}

type SupportFile {
  commentId: String
  fileSize: Int!
  fileUrl: String!
  filename: String!
  id: String!
  mimeType: String!
  ticketId: String
  uploadedAt: DateTime!
  uploadedBy: String!
}

type Ticket {
  accountId: String
  assignedTo: [String!]
  category: Category!
  comments: [Comment!]
  createdAt: DateTime!
  createdBy: String!
  description: String!
  files: [SupportFile!]
  id: ID!
  lastUpdatedBy: String!
  partnerId: String
  priority: TicketPriority!
  status: String!
  subject: String!
  updatedAt: DateTime!
}

"""The priority of the ticket"""
enum TicketPriority {
  HIGH
  LOW
  MEDIUM
  URGENT
}

"""The status of the ticket"""
enum TicketStatus {
  CLOSED
  IN_PROGRESS
  OPEN
  RESOLVED
}

input UpdateCategoryDto {
  autoAssignTo: [String]
  description: String
  escalateTo: String
  name: String
  timeoutMinutes: Int
  type: CategoryType
}

input UpdateCustomValueDto {
  category: String
  description: String
  name: String
  type: CustomValueType
  value: String
}

input UpdateTicketDto {
  accountId: String
  assignedTo: [String!]
  categoryId: String
  description: String
  partnerId: String
  priority: TicketPriority
  status: TicketStatus
  subject: String
}

"""The `Upload` scalar type represents a file upload."""
scalar Upload

type User {
  accountId: String
  active: Boolean!
  country: String
  createdAt: DateTime!
  email: String!
  firstName: String!
  id: ID!
  lastName: String!
  mfaEnabled: Boolean!
  partnerId: [String!]!
  phone: String
  role: String!
  verifiedEmail: Boolean!
  verifiedPhone: Boolean!
}

type UserListResponse {
  items: [User!]!
  limit: Float!
  page: Float!
  pages: Float!
  total: Float!
}