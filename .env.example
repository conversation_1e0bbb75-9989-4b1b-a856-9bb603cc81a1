# API configuration
API_PORT=3040
API_PREFIX=api/v1

# Swagger API documentation
SWAGGER_ENABLE=true

# Database ORM configuration
DATABASE_URL=************************************/supportv2?schema=public

# JWT configuration
JWT_SECRET=your_jwt_secret

# Access to the health route
HEALTH_TOKEN=ThisMustBeChanged

# Logging configuration
LOG=true

# OpenObserve Logging configuration
OTEL_LOGS=false
# OTEL_USER=<EMAIL>
# OTEL_PASSWORD=YourPassword
# OTEL_HOST=https://ngnair-logs.ngcap.ngnair.com
# OTEL_ORG=default
# OTEL_STREAM=quickstart1


# Backblaze B2 configuration
# B2_KEY_ID=0057ca20a119bc10000000005
# B2_APP_KEY=K005KJ/zZeDrMCCGxGhLTKOum5YeY/Y
# B2_BUCKET_NAME=ngnair-support
# B2_BUCKET_ID=270cdaf2b00a6111997b0c11
# B2_ENDPOINT=s3.us-east-005.backblazeb2.com

# Application Settings
PORT=3040
NODE_ENV=development
ALLOWED_ORIGINS=http://ng-support-fe-local.dev.dev1.ngnair.com:3041,http://ng-support-admin-local.dev.dev1.ngnair.com:3042,https://ng-auth-dev.dev1.ngnair.com

# External Authentication Server
AUTH_SERVICE_URL=https://ng-auth-dev.dev1.ngnair.com
AUTH_JWKS_URL=https://ng-auth-dev.dev1.ngnair.com/api/v1/jwks
ACCESS_TOKEN_ENCRYPTION_KEY=b7e2c1a4d8f3e6b9c2f7a1e4d3b8c6f2e1a7b4c9d6e3f8a2b5c4d7e1f6a3b2c8

# Frontend and Admin URLs for CORS
FRONTEND_URL=http://ng-support-fe-local.dev.dev1.ngnair.com:3041
ADMIN_URL=http://ng-support-admin-local.dev.dev1.ngnair.com:3042
AUTH_URL=https://ng-auth-dev.dev1.ngnair.com

# Backblaze B2 configuration
B2_ENDPOINT=s3.us-east-005.backblazeb2.com

# End of configuration