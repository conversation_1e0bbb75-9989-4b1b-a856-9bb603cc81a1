import { Resolver, Query, Mutation, Args, Context } from '@nestjs/graphql';
import { UseGuards, NotFoundException, BadRequestException } from '@nestjs/common';
import { TicketService } from './ticket.service';
import { TicketModel } from './ticket.model';
import { CreateTicketInput } from './ticket.input';
import { UpdateTicketDto } from './dto/update-ticket.dto';
import { GraphQLAuthGuard } from '../auth/src/guards';
import { ScopeGuard, PartnerSupportAccess } from '../auth/src/guards/scope.guard';
import { UserDataService } from '../auth/src/user-data.service';
import { ScopeContext } from '../auth/src/types/scope.types';

@Resolver(() => TicketModel)
@UseGuards(GraphQLAuthGuard, ScopeGuard)
@PartnerSupportAccess()
export class TicketResolver {
  constructor(
    private readonly ticketService: TicketService,
    private readonly userDataService: UserDataService
  ) {}

  @Query(() => [TicketModel], { name: 'tickets' })
  async tickets(@Context() context: any) {
    // Extract scope context from request
    const scopeContext: ScopeContext = context.req.scopeContext;

    // Use scope-aware ticket retrieval
    return this.ticketService.findManyWithScope(scopeContext);
  }

  @Mutation(() => TicketModel)
  async createTicket(
    @Args('data') data: CreateTicketInput,
    @Context() context: any
  ) {
    const user = context.req.user;
    const userId = user.sub || user.id;
    const scopeContext: ScopeContext = context.req.scopeContext;

    // Extract access token from cookies for user data fetching
    const accessToken = this.userDataService.extractAccessToken(context.req.cookies);

    // Fetch user data from auth API - handle gracefully if it fails
    let userData = null;
    try {
      userData = await this.userDataService.getCachedUserData(userId, accessToken);
      console.log(`✅ [TICKET RESOLVER] User data fetched successfully for ${userId}:`, userData);
    } catch (userDataError) {
      console.warn(`⚠️ [TICKET RESOLVER] Failed to fetch user data for ${userId}:`, userDataError.message);
    }

    // Extract partner information from scope context
    const partnerInfo = scopeContext.partnerInfo;
    console.log(`🏢 [TICKET RESOLVER] Partner info from scope context:`, partnerInfo);

    // Prepare ticket data according to requirements
    const ticketData = {
      ...data,
      createdBy: userId,
      lastUpdatedBy: userId,
      // User data fields from /auth/users/{id} endpoint
      firstName: userData?.first_name,
      lastName: userData?.last_name,
      email: userData?.email,
      // Partner information fields as per requirements
      partnerUserId: userId, // Store user's ID (sub from JWT) in partnerUserId field
      partnerRole: scopeContext.scope, // Store user's scope in partnerRole field
      partnerOrgId: partnerInfo?.public_uid, // Store partner's public_uid in partnerOrgId field
      entSet: scopeContext.tenantType, // Tenant type (partner/account)
    };

    console.log(`📝 [TICKET RESOLVER] Creating ticket with data:`, ticketData);

    // Use scope-aware creation
    return this.ticketService.createWithScope(scopeContext, ticketData);
  }

  @Query(() => TicketModel, { name: 'ticket' })
  async getTicket(@Args('id') id: string, @Context() context: any) {
    const scopeContext: ScopeContext = context.req.scopeContext;

    // Use scope-aware ticket retrieval
    const ticket = await this.ticketService.findUniqueWithScope(scopeContext, { id });
    if (!ticket) {
      throw new NotFoundException(`Ticket with ID ${id} not found`);
    }
    return ticket;
  }

  @Mutation(() => TicketModel)
  async updateTicket(
    @Args('id') id: string,
    @Args('data') data: UpdateTicketDto,
    @Context() context: any
  ) {
    const user = context.req.user;
    const scopeContext: ScopeContext = context.req.scopeContext;

    try {
      const result = await this.ticketService.updateWithScope(scopeContext, { id }, {
        ...data,
        lastUpdatedBy: user.sub || user.id
      });

      return result;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      throw new BadRequestException(
        `An error occurred while updating the ticket: ${errorMessage}`
      );
    }
  }

  @Mutation(() => TicketModel)
  @UseGuards(GraphQLAuthGuard)
  async deleteTicket(@Args('id') id: string) {
    // Check if ticket exists
    const existingTicket = await this.ticketService.findUnique({ id });
    if (!existingTicket) {
      throw new NotFoundException(`Ticket with ID ${id} not found`);
    }

    try {
      const result = await this.ticketService.delete({ id });
      if (!result) {
        throw new NotFoundException(`Ticket with ID ${id} not found`);
      }
      return result;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      throw new BadRequestException(
        `An error occurred while deleting the ticket: ${errorMessage}`
      );
    }
  }
}
