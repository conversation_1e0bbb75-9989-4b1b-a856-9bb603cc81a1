import { Controller, Get, Post, Body, Param, Put, Delete, UseGuards, NotFoundException, BadRequestException, Request } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { Prisma } from "@prisma/client";
import { ApiGuard } from "../auth/src/guards";
import { ScopeGuard, SupportAccess, CategoryManagement, GlobalAdminOnly, PartnerSupportAccess } from "../auth/src/guards/scope.guard";

// Debug: Verify imports are working
console.log('🔧 [CATEGORY CONTROLLER] GlobalAdminOnly imported:', typeof GlobalAdminOnly);
import { AuthenticatedRequest } from "../auth/src/types";
import { ScopeContext } from "../auth/src/types/scope.types";
import { CreateCategoryDto } from "./dto/create-category.dto";
import { UpdateCategoryDto } from "./dto/update-category.dto";

import { CategoryService } from "./category.service";
import { CategoryPayload } from "./types/category.types";

@ApiTags("Categories")
@Controller("categories")
@UseGuards(ApiGuard, ScopeGuard)
@ApiBearerAuth()
export class CategoryController {
  constructor(private readonly categoryService: CategoryService) {}

  @Post()
  @GlobalAdminOnly()
  @ApiOperation({ summary: "Create a new category (Global Admin Only)" })
  @ApiResponse({ status: 201, description: "Category created successfully" })
  @ApiResponse({ status: 401, description: "Unauthorized - Global Admin required" })
  @ApiResponse({ status: 409, description: "Category with this name already exists" })
  async create(@Body() createCategoryDto: CreateCategoryDto): Promise<CategoryPayload> {
    console.log('🔧 [CATEGORY CONTROLLER] create method called - decorator should have set metadata');
    return this.categoryService.create(createCategoryDto);
  }

  @Get()
  @PartnerSupportAccess()
  @ApiOperation({ summary: "Get all categories (Partner Support Access)" })
  @ApiResponse({ status: 200, description: "List of all categories" })
  @ApiResponse({ status: 401, description: "Unauthorized - Partner support access required" })
  async findAll(): Promise<CategoryPayload[]> {
    return this.categoryService.findMany({});
  }

  @Get(":id")
  @ApiOperation({ summary: "Get category by id" })
  @ApiResponse({ status: 200, description: "Category found" })
  @ApiResponse({ status: 404, description: "Category not found" })
  async findOne(@Param("id") id: string): Promise<CategoryPayload> {
    const category = await this.categoryService.findUnique({ id });
    if (!category) {
      throw new NotFoundException(`Category with ID ${id} not found`);
    }
    return category;
  }

  @Put(":id")
  @GlobalAdminOnly()
  @ApiOperation({ summary: "Update a category (Global Admin Only)" })
  @ApiResponse({ status: 200, description: "Category updated successfully" })
  @ApiResponse({ status: 403, description: "Insufficient permissions - Global Admin required" })
  @ApiResponse({ status: 404, description: "Category not found" })
  @ApiResponse({ status: 409, description: "Category with this name already exists" })
  async update(
    @Param("id") id: string,
    @Body() updateCategoryDto: UpdateCategoryDto
  ): Promise<CategoryPayload> {
    try {
      return await this.categoryService.update(id, updateCategoryDto);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === "P2002") {
        throw new BadRequestException("A category with this name already exists");
      }
      throw error;
    }
  }

  @Delete(":id")
  @GlobalAdminOnly()
  @ApiOperation({ summary: "Delete a category (Global Admin Only)" })
  @ApiResponse({ status: 200, description: "Category deleted successfully" })
  @ApiResponse({ status: 403, description: "Insufficient permissions - Global Admin required" })
  @ApiResponse({ status: 404, description: "Category not found" })
  @ApiResponse({ status: 400, description: "Cannot delete category with associated tickets" })
  async remove(@Param("id") id: string): Promise<CategoryPayload> {
    try {
      return await this.categoryService.delete(id);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === "P2003") {
        throw new BadRequestException("Cannot delete category that has associated tickets");
      }
      throw error;
    }
  }
}
