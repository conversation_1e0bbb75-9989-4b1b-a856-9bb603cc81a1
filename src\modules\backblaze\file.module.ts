import { Module, forwardRef } from "@nestjs/common";
import { PrismaService } from "../../prisma/prisma.service";
import { FileService } from "./file.service";
import { StorageModule } from "./storage";
import { FileController } from "./file.controller";
import { SupportFileResolver } from "./support-file.resolver";
import { AuthModule } from "../auth/src/auth.module";

@Module({
  imports: [StorageModule, forwardRef(() => AuthModule)],
  controllers: [FileController],
  providers: [FileService, PrismaService, SupportFileResolver],
  exports: [FileService],
})
export class FileModule {}
