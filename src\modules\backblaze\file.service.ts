import { Injectable, Logger, NotFoundException, BadRequestException } from "@nestjs/common";
import { PrismaClient, Prisma, SupportFile } from "@prisma/client";
import { BackblazeService } from "./storage";
import { PrismaService } from "../../prisma/prisma.service";
import { FastifyFileUpload } from "./file.controller";
import { ScopeFilterService } from "../auth/src/scope-filter.service";
import { ScopeContext } from "../auth/src/types/scope.types";

type SupportFileDelegate = PrismaClient["supportFile"];

@Injectable()
export class FileService {
  private readonly logger = new Logger(FileService.name);
  private readonly db: SupportFileDelegate;

  constructor(
    private readonly prisma: PrismaService,
    private readonly backblazeService: BackblazeService,
    private readonly scopeFilterService: ScopeFilterService,
  ) {
    this.db = prisma.supportFile;
  }

  async findById(id: string): Promise<SupportFile | null> {
    return this.db.findUnique({
      where: { id }
    });
  }

  /**
   * Validate file association parameters
   */
  private validateFileAssociation(ticketId?: string, commentId?: string): void {
    // Helper function to check if a value is considered "empty"
    const isEmpty = (val: any): boolean => {
      return val === null || val === undefined || val === '' || (typeof val === 'string' && val.trim() === '');
    };

    const hasTicketId = !isEmpty(ticketId);
    const hasCommentId = !isEmpty(commentId);

    // Exactly one should be provided (XOR logic)
    if (!hasTicketId && !hasCommentId) {
      throw new BadRequestException("File must be associated with either a ticket or comment");
    }

    if (hasTicketId && hasCommentId) {
      throw new BadRequestException("Cannot associate file with both ticket and comment");
    }
  }

  async uploadFile(
    file: FastifyFileUpload,
    userId: string,
    ticketId?: string,
    commentId?: string,
  ): Promise<SupportFile> {
    try {
      // Defensive: ensure file is a plain object with only the expected properties
      if (
        !file ||
        typeof file.originalname !== "string" ||
        typeof file.mimetype !== "string" ||
        typeof file.encoding !== "string" ||
        !Buffer.isBuffer(file.buffer) ||
        typeof file.size !== "number" ||
        typeof file.fieldname !== "string"
      ) {
        throw new BadRequestException("Invalid file object passed to service");
      }

      // Validate file association
      this.validateFileAssociation(ticketId, commentId);

      // Upload file to Backblaze
      const path = ticketId ? `tickets/${ticketId}` : `comments/${commentId}`;
      const filename = `${path}/${Date.now()}-${file.originalname}`;
      // Only pass plain file data to storage
      const { url } = await this.backblazeService.uploadFile(
        {
          originalname: file.originalname,
          mimetype: file.mimetype,
          encoding: file.encoding,
          buffer: file.buffer,
          size: file.size,
          fieldname: file.fieldname,
        },
        filename
      );

      // First validate entities exist if IDs are provided
      if (ticketId) {
        const ticket = await this.prisma.ticket.findUnique({ 
          where: { id: ticketId } 
        });
        if (!ticket) throw new NotFoundException("Ticket not found");
      }

      if (commentId) {
        const comment = await this.prisma.comment.findUnique({
          where: { id: commentId },
          include: { ticket: true }
        });
        if (!comment) throw new NotFoundException("Comment not found");
        if (!comment.ticket) throw new BadRequestException("Comment must belong to a ticket");
      }

      // Prepare plain data for Prisma
      const createData = {
        filename: file.originalname,
        fileUrl: url,
        fileSize: file.size,
        mimeType: file.mimetype,
        uploadedBy: userId,
        ...(ticketId && { ticketId }),
        ...(commentId && { commentId })
      } satisfies Prisma.SupportFileCreateInput;

      // Debug log: show only plain data
      this.logger.log(`Creating file record: ${JSON.stringify({
        filename: createData.filename,
        fileUrl: createData.fileUrl,
        fileSize: createData.fileSize,
        mimeType: createData.mimeType,
        uploadedBy: createData.uploadedBy,
        ticketId: createData.ticketId,
        commentId: createData.commentId
      })}`);
      // Restore the real Prisma create call
      return this.db.create({
        data: createData
      });
    } catch (error) {
      this.logger.error("Failed to upload file:", error instanceof Error ? error.message : "Unknown error");
      throw error;
    }
  }

  async getFiles(ticketId?: string, commentId?: string): Promise<SupportFile[]> {
    const queryInput = {
      where: {
        OR: [
          ...(ticketId ? [{ ticketId }] : []),
          ...(commentId ? [{ commentId }] : [])
        ]
      }
    } satisfies Prisma.SupportFileFindManyArgs;

    return this.db.findMany(queryInput);
  }

  async deleteFile(fileId: string): Promise<SupportFile> {
    // Find the file first
    const whereInput: Prisma.SupportFileWhereUniqueInput = { id: fileId };
    const file = await this.prisma.supportFile.findUnique({ where: whereInput });

    if (!file) {
      throw new NotFoundException("File not found");
    }

    try {
      // Extract key from URL
      const fileUrl = String(file.fileUrl);
      const url = new URL(fileUrl);
      const key = url.pathname.substring(1); // Remove leading slash

      // Delete from Backblaze
      await this.backblazeService.deleteFile(key);

      // Delete from database
      return await this.prisma.supportFile.delete({ where: whereInput });
    } catch (error) {
      this.logger.error("Failed to delete file:", error instanceof Error ? error.message : "Unknown error");
      throw error;
    }
  }

  // Scope-aware methods

  /**
   * Upload file with scope validation
   */
  async uploadFileWithScope(
    file: FastifyFileUpload,
    scopeContext: ScopeContext,
    ticketId?: string,
    commentId?: string,
  ): Promise<SupportFile> {
    this.logger.log(`📁 [FILE SERVICE] Uploading file with scope validation for user ${scopeContext.userId}`);

    // Validate file association
    this.validateFileAssociation(ticketId, commentId);

    // If ticketId is provided, validate user can access the ticket
    if (ticketId) {
      const ticket = await this.prisma.ticket.findUnique({
        where: { id: ticketId },
        include: {
          // Include necessary fields for scope validation
          category: true
        }
      });
      if (!ticket) {
        throw new NotFoundException("Ticket not found");
      }

      // Check if user can access this ticket
      if (!this.scopeFilterService.canAccessTicket(scopeContext, ticket)) {
        throw new BadRequestException("You don't have permission to upload files to this ticket");
      }
    }

    // If commentId is provided, validate user can access the comment's ticket
    if (commentId) {
      const comment = await this.prisma.comment.findUnique({
        where: { id: commentId },
        include: {
          ticket: {
            include: {
              category: true
            }
          }
        }
      });
      if (!comment) {
        throw new NotFoundException("Comment not found");
      }
      if (!comment.ticket) {
        throw new BadRequestException("Comment must belong to a ticket");
      }

      // Check if user can access this comment's ticket
      if (!this.scopeFilterService.canAccessTicket(scopeContext, comment.ticket)) {
        throw new BadRequestException("You don't have permission to upload files to this comment");
      }
    }

    // Use the existing uploadFile method
    return this.uploadFile(file, scopeContext.userId, ticketId, commentId);
  }

  /**
   * Get files with scope filtering
   */
  async getFilesWithScope(
    scopeContext: ScopeContext,
    ticketId?: string,
    commentId?: string
  ): Promise<SupportFile[]> {
    this.logger.log(`📁 [FILE SERVICE] Getting files with scope filtering for user ${scopeContext.userId}`);

    let where: Prisma.SupportFileWhereInput = {};

    // Apply ticket/comment filters
    if (ticketId) {
      where.ticketId = ticketId;
    }
    if (commentId) {
      where.commentId = commentId;
    }

    // Apply scope-based filtering
    const scopedWhere = this.scopeFilterService.applyFileFilters(scopeContext, where);

    return this.prisma.supportFile.findMany({
      where: scopedWhere,
      include: {
        ticket: {
          include: {
            category: true
          }
        },
        comment: {
          include: {
            ticket: {
              include: {
                category: true
              }
            }
          }
        }
      },
      orderBy: { uploadedAt: "desc" }
    });
  }

  /**
   * Delete file with scope validation
   */
  async deleteFileWithScope(fileId: string, scopeContext: ScopeContext): Promise<SupportFile> {
    this.logger.log(`📁 [FILE SERVICE] Deleting file with scope validation for user ${scopeContext.userId}`);

    // Find the file with related data for scope validation
    const file = await this.prisma.supportFile.findUnique({
      where: { id: fileId },
      include: {
        ticket: {
          include: {
            category: true
          }
        },
        comment: {
          include: {
            ticket: {
              include: {
                category: true
              }
            }
          }
        }
      }
    });

    if (!file) {
      throw new NotFoundException("File not found");
    }

    // Check if user can delete this file
    if (!this.scopeFilterService.canDeleteFile(scopeContext, file)) {
      throw new BadRequestException("You don't have permission to delete this file");
    }

    // Use the existing deleteFile method
    return this.deleteFile(fileId);
  }

  /**
   * Get file by ID with scope validation
   */
  async getFileByIdWithScope(fileId: string, scopeContext: ScopeContext): Promise<SupportFile> {
    this.logger.log(`📁 [FILE SERVICE] Getting file by ID with scope validation for user ${scopeContext.userId}`);

    // Find the file with related data for scope validation
    const file = await this.prisma.supportFile.findUnique({
      where: { id: fileId },
      include: {
        ticket: {
          include: {
            category: true
          }
        },
        comment: {
          include: {
            ticket: {
              include: {
                category: true
              }
            }
          }
        }
      }
    });

    if (!file) {
      throw new NotFoundException("File not found");
    }

    // Check if user can access this file
    if (!this.scopeFilterService.canAccessFile(scopeContext, file)) {
      throw new BadRequestException("You don't have permission to access this file");
    }

    return file;
  }
}
