name: Deploy to Production

env:
  CONTEXT_DIR: "./"
  IMAGE_NAME: ${{ github.repository }}/build
  DOCKERFILE: Dockerfile
  DOCKER_REGISTRY: docker.io
  OWNER: blee900
  DOCKER_REPOSITORY: ng-support

on:
  push:
    branches:
      - main
    paths-ignore:
      - ".github/workflows/**"
    # you can specify path if you have a monorepo and you want to deploy if particular directory is changed, make sure to update `CONTEXT_DIR` too
    # paths:
    #   - "backend-app/**"

jobs:
  build-and-publish:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v1

      - name: Set environment variables
        run: |
          echo "OWNER=${{ secrets.DOCKER_USERNAME }}" >> $GITHUB_ENV
          echo "DOCKER_REPOSITORY=${{ secrets.DOCKER_REPOSITORY }}" >> $GITHUB_ENV
          echo "CAPROVER_APP=${{ secrets.CAPROVER_APP }}" >> $GITHUB_ENV

      - name: Log in to the Container registry
        uses: docker/login-action@f054a8b539a109f9f41c372932f1ae047eff08c9
        with:
          registry: ${{ env.DOCKER_REGISTRY }}
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}
      - name: Preset Image Name
        run: echo "IMAGE_URL=$(echo ${{ env.DOCKER_REGISTRY }}/${{ env.OWNER }}/${{ env.DOCKER_REPOSITORY }}:$(echo ${{ github.sha }} | cut -c1-7) | tr '[:upper:]' '[:lower:]')" >> $GITHUB_ENV
      - name: Build and push Docker Image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          push: true
          tags: ${{ env.IMAGE_URL }}
      - name: Deploy to CapRover
        uses: caprover/deploy-from-github@d76580d79952f6841c453bb3ed37ef452b19752c
        with:
          server: ${{ secrets.CAPROVER_HOST }}
          app: ${{ env.CAPROVER_APP }}
          token: "${{ secrets.CAPROVER_APP_TOKEN }}"
          image: ${{ env.IMAGE_URL }}
