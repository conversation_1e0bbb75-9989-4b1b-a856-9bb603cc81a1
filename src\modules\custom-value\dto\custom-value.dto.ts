import { InputType, Field, registerEnumType } from "@nestjs/graphql";
import { ApiProperty } from "@nestjs/swagger";
import { CustomValueType } from "@prisma/client";
import { IsString, IsEnum, IsBoolean, IsOptional } from "class-validator";

registerEnumType(CustomValueType, {
  name: "CustomValueType",
  description: "The type of the custom value for validation and parsing",
});

@InputType()
export class CreateCustomValueDto {
  @ApiProperty({ 
    description: "Unique identifier like DEFAULT_TIMEOUT_MINUTES or WELCOME_MESSAGE",
    example: "DEFAULT_TIMEOUT_MINUTES" 
  })
  @IsString()
  @Field()
  key: string;

  @ApiProperty({ 
    description: "Display name",
    example: "Default Timeout Minutes" 
  })
  @IsString()
  @Field()
  name: string;

  @ApiProperty({ 
    description: "Description of what this value is used for",
    required: false,
    example: "Default timeout in minutes before ticket escalation" 
  })
  @IsOptional()
  @IsString()
  @Field({ nullable: true })
  description?: string;

  @ApiProperty({ 
    description: "The value stored as string, will be parsed based on type",
    example: "60" 
  })
  @IsString()
  @Field()
  value: string;

  @ApiProperty({ 
    description: "Type helps in validation and parsing",
    enum: CustomValueType 
  })
  @IsEnum(CustomValueType)
  @Field(() => CustomValueType)
  type: CustomValueType;

  @ApiProperty({ 
    description: "Category for grouping: TICKET, NOTIFICATION, SYSTEM, etc.",
    example: "TICKET" 
  })
  @IsString()
  @Field()
  category: string;

  @ApiProperty({ 
    description: "If true, cannot be deleted, only modified",
    default: false 
  })
  @IsBoolean()
  @IsOptional()
  @Field({ nullable: true })
  isSystem?: boolean;
}

@InputType()
export class UpdateCustomValueDto {
  @ApiProperty({ 
    description: "Display name",
    example: "Default Timeout Minutes",
    required: false
  })
  @IsString()
  @IsOptional()
  @Field({ nullable: true })
  name?: string;

  @ApiProperty({ 
    description: "Description of what this value is used for",
    required: false,
    example: "Default timeout in minutes before ticket escalation" 
  })
  @IsString()
  @IsOptional()
  @Field({ nullable: true })
  description?: string;

  @ApiProperty({ 
    description: "The value stored as string, will be parsed based on type",
    example: "60",
    required: false
  })
  @IsString()
  @IsOptional()
  @Field({ nullable: true })
  value?: string;

  @ApiProperty({ 
    description: "Type helps in validation and parsing",
    enum: CustomValueType,
    required: false
  })
  @IsEnum(CustomValueType)
  @IsOptional()
  @Field(() => CustomValueType, { nullable: true })
  type?: CustomValueType;

  @ApiProperty({ 
    description: "Category for grouping: TICKET, NOTIFICATION, SYSTEM, etc.",
    example: "TICKET",
    required: false
  })
  @IsString()
  @IsOptional()
  @Field({ nullable: true })
  category?: string;
}
