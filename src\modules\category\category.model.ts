import { ObjectType, Field } from '@nestjs/graphql';

@ObjectType()
export class Category {
  @Field(() => String)
  id: string;

  @Field()
  name: string;

  @Field({ nullable: true })
  description?: string;

  @Field()
  type: string;

  @Field(() => [String])
  autoAssignTo: string[];

  @Field()
  timeoutMinutes: number;

  @Field({ nullable: true })
  escalateTo?: string;

  @Field()
  createdAt: Date;

  @Field()
  updatedAt: Date;
}
