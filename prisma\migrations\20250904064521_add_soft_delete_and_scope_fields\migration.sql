-- AlterTable
ALTER TABLE "Category" ADD COLUMN     "deletedAt" TIMESTAMP(3);

-- AlterTable
ALTER TABLE "Comment" ADD COLUMN     "deletedAt" TIMESTAMP(3);

-- AlterTable
ALTER TABLE "Ticket" ADD COLUMN     "deletedAt" TIMESTAMP(3),
ADD COLUMN     "sub" TEXT;

-- CreateIndex
CREATE INDEX "Category_deletedAt_idx" ON "Category"("deletedAt");

-- CreateIndex
CREATE INDEX "Comment_deletedAt_idx" ON "Comment"("deletedAt");

-- CreateIndex
CREATE INDEX "Comment_ticketId_deletedAt_idx" ON "Comment"("ticketId", "deletedAt");

-- CreateIndex
CREATE INDEX "Ticket_deletedAt_idx" ON "Ticket"("deletedAt");

-- CreateIndex
CREATE INDEX "Ticket_partnerId_deletedAt_idx" ON "Ticket"("partnerId", "deletedAt");

-- CreateIndex
CREATE INDEX "Ticket_accountId_deletedAt_idx" ON "Ticket"("accountId", "deletedAt");

-- CreateIndex
CREATE INDEX "Ticket_sub_idx" ON "Ticket"("sub");
