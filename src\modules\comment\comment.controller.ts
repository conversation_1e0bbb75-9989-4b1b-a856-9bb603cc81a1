import { <PERSON>, Get, Post, Body, Param, Delete, UseGuards, Request } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { ApiGuard } from "../auth/src/guards";
import { ScopeGuard, SupportAccess, RequirePermissions } from "../auth/src/guards/scope.guard";
import { AuthenticatedRequest } from "../auth/src/types";
import { ScopeContext } from "../auth/src/types/scope.types";
import { UserDataService } from "../auth/src/user-data.service";
import { CreateCommentDto } from "./dto/create-comment.dto";
import { CommentService } from "./comment.service";
import { CommentPayload } from "./types/comment.types";

@ApiTags("Comments")
@Controller("comments")
@UseGuards(ApiGuard, ScopeGuard)
@SupportAccess()
@ApiBearerAuth()
export class CommentController {
  constructor(
    private readonly commentService: CommentService,
    private readonly userDataService: UserDataService
  ) {}

  @Post()
  @RequirePermissions('canCreateComments')
  @ApiOperation({ summary: "Create a new comment" })
  @ApiResponse({
    status: 201,
    description: "Comment created successfully",
    schema: {
      example: {
        id: "e3105fa6-7a81-4aaa-b0e2-50d646102dee",
        message: "This is a comment on the ticket",
        authorId: "f7b98e6f-95af-4d54-9c53-312ada49ba6e",
        ticketId: "200ee76d-fa54-4f5d-b43a-82a56c0b8084",
        createdAt: "2025-09-17T09:46:20.234Z",
        updatedAt: "2025-09-17T09:46:20.234Z",
        firstName: "John",
        lastName: "Doe",
        email: "<EMAIL>",
        partnerUserId: "f7b98e6f-95af-4d54-9c53-312ada49ba6e",
        partnerRole: "Partner Admin",
        partnerOrgId: "org-123",
        entSet: "partner",
        authorName: "John Doe",
        authorEmail: "<EMAIL>"
      }
    }
  })
  @ApiResponse({ status: 400, description: "Invalid input" })
  @ApiResponse({ status: 404, description: "Ticket not found or access denied" })
  @ApiResponse({ status: 500, description: "Internal server error" })
  async create(
    @Body() createCommentDto: CreateCommentDto,
    @Request() req: AuthenticatedRequest & { scopeContext: ScopeContext }
  ): Promise<CommentPayload> {
    try {
      // Extract user ID from JWT
      const userId = req.user.sub || req.user.id;

      // Extract access token from cookies for user data fetching
      const accessToken = this.userDataService.extractAccessToken((req as any).cookies);

      // Fetch user data from auth API - handle gracefully if it fails
      let userData = null;
      try {
        userData = await this.userDataService.getCachedUserData(userId, accessToken);
        console.log(`✅ [COMMENT CONTROLLER] User data fetched successfully for ${userId}:`, userData);
      } catch (userDataError) {
        console.warn(`⚠️ [COMMENT CONTROLLER] Failed to fetch user data for ${userId}:`, userDataError.message);
        // Continue with comment creation even if user data fetch fails
      }

      // Extract partner information from scope context
      const partnerInfo = req.scopeContext.partnerInfo;
      console.log(`🏢 [COMMENT CONTROLLER] Partner info from scope context:`, partnerInfo);

      // Prepare comment data with all available information (same as ticket system)
      const commentData = {
        message: createCommentDto.message,
        ticketId: createCommentDto.ticketId,
        authorId: userId,
        // User data fields from /auth/users/{id} endpoint
        firstName: userData?.first_name,
        lastName: userData?.last_name,
        email: userData?.email,
        // Partner information fields as per requirements
        partnerUserId: userId, // Store user's ID (sub from JWT) in partnerUserId field
        partnerRole: req.scopeContext.scope, // Store user's scope in partnerRole field
        partnerOrgId: partnerInfo?.public_uid, // Store partner's public_uid in partnerOrgId field
        entSet: req.scopeContext.tenantType, // Tenant type (partner/account)
        // User display fields for comment model
        authorName: userData ? `${userData.first_name} ${userData.last_name}`.trim() : undefined,
        authorEmail: userData?.email
      };

      console.log(`📝 [COMMENT CONTROLLER] Creating comment with data:`, commentData);

      // Use scope-aware comment creation
      return await this.commentService.createWithScope(req.scopeContext, commentData);
    } catch (error) {
      console.error('Error creating comment:', error);
      throw error;
    }
  }

  @Get("ticket/:ticketId")
  @ApiOperation({ summary: "Get all comments for a ticket" })
  @ApiResponse({
    status: 200,
    description: "Comments retrieved successfully",
    schema: {
      type: "array",
      items: {
        type: "object",
        example: {
          id: "e3105fa6-7a81-4aaa-b0e2-50d646102dee",
          message: "This is a comment on the ticket",
          authorId: "f7b98e6f-95af-4d54-9c53-312ada49ba6e",
          ticketId: "200ee76d-fa54-4f5d-b43a-82a56c0b8084",
          createdAt: "2025-09-17T09:46:20.234Z",
          updatedAt: "2025-09-17T09:46:20.234Z",
          firstName: "John",
          lastName: "Doe",
          email: "<EMAIL>",
          partnerUserId: "f7b98e6f-95af-4d54-9c53-312ada49ba6e",
          partnerRole: "Partner Admin",
          partnerOrgId: "org-123",
          entSet: "partner",
          authorName: "John Doe",
          authorEmail: "<EMAIL>"
        }
      }
    }
  })
  @ApiResponse({ status: 404, description: "Ticket not found or access denied" })
  async findByTicketId(
    @Param("ticketId") ticketId: string,
    @Request() req: AuthenticatedRequest & { scopeContext: ScopeContext }
  ): Promise<CommentPayload[]> {
    // Use scope-aware comment retrieval
    return this.commentService.findByTicketIdWithScope(req.scopeContext, ticketId);
  }

  @Delete(":id")
  @RequirePermissions('canDeleteComments')
  @ApiOperation({ summary: "Delete a comment" })
  @ApiResponse({ status: 200, description: "Comment deleted successfully" })
  @ApiResponse({ status: 404, description: "Comment not found or access denied" })
  async remove(
    @Param("id") id: string,
    @Request() req: AuthenticatedRequest & { scopeContext: ScopeContext }
  ): Promise<CommentPayload> {
    // Use scope-aware comment deletion
    return this.commentService.deleteWithScope(req.scopeContext, { id });
  }
}
