-- CreateTable
CREATE TABLE "SupportFile" (
    "id" TEXT NOT NULL,
    "filename" TEXT NOT NULL,
    "fileSize" INTEGER NOT NULL,
    "fileUrl" TEXT NOT NULL,
    "mimeType" TEXT NOT NULL,
    "uploadedBy" TEXT NOT NULL,
    "ticketId" TEXT,
    "commentId" TEXT,
    "uploadedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "SupportFile_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "SupportFile" ADD CONSTRAINT "SupportFile_ticketId_fkey" FOREIGN KEY ("ticketId") REFERENCES "Ticket"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Add<PERSON>ore<PERSON><PERSON>ey
ALTER TABLE "SupportFile" ADD CONSTRAINT "SupportFile_commentId_fkey" FOREIGN KEY ("commentId") REFERENCES "Comment"("id") ON DELETE CASCADE ON UPDATE CASCADE;
