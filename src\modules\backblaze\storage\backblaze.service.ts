import { S3Client, PutObjectCommand, DeleteObjectCommand } from "@aws-sdk/client-s3";
import { NodeHttpHandler } from "@aws-sdk/node-http-handler";
import { Injectable, Logger, OnModuleInit } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { FastifyFileUpload } from "../file.controller";
import { StorageConfig } from "./storage.types";

@Injectable()
export class BackblazeService implements OnModuleInit {
  private s3Client: S3Client | null;
  private readonly logger = new Logger(BackblazeService.name);
  private readonly bucketId: string;
  private readonly bucketName: string;
  private readonly endpoint: string;

  constructor(private readonly configService: ConfigService<StorageConfig>) {
    let keyId: string;
    let appKey: string;

    try {
      keyId = this.configService.get<string>("B2_KEY_ID") || "";
      appKey = this.configService.get<string>("B2_APP_KEY") || "";
      this.endpoint = this.configService.get<string>("B2_ENDPOINT") || "";
      this.bucketId = this.configService.get<string>("B2_BUCKET_ID") || "";
      this.bucketName = "ngnair-support"; // Use the bucket name instead of ID

      // Check if all required configuration is available
      if (!keyId || !appKey || !this.endpoint || !this.bucketId) {
        this.logger.warn("⚠️ Backblaze configuration incomplete. File upload functionality will be disabled.");
        this.logger.warn("Missing configuration:", {
          B2_KEY_ID: !keyId ? "missing" : "present",
          B2_APP_KEY: !appKey ? "missing" : "present",
          B2_ENDPOINT: !this.endpoint ? "missing" : "present",
          B2_BUCKET_ID: !this.bucketId ? "missing" : "present"
        });
        this.s3Client = null;
        return;
      }

      this.logger.log("Successfully loaded Backblaze B2 configuration");
      this.logger.debug(`Using endpoint: ${this.endpoint}`);
      this.logger.debug(`Using bucket: ${this.bucketName}`);
    } catch (error: unknown) {
      const err = error as Error;
      this.logger.error("❌ Error initializing Backblaze configuration:", err.message);
      this.logger.warn("⚠️ File upload functionality will be disabled.");
      this.s3Client = null;
      return;
    }

    // Create a custom HTTP handler that doesn't add checksum headers
    const requestHandler = new NodeHttpHandler({
      httpAgent: undefined,
      httpsAgent: undefined,
    });

    this.s3Client = new S3Client({
      endpoint: `https://${this.endpoint}`,
      region: "us-east-005",
      credentials: {
        accessKeyId: keyId,
        secretAccessKey: appKey,
      },
      forcePathStyle: true,
      requestHandler,
    });
  }

  async onModuleInit(): Promise<void> {
    if (!this.s3Client) {
      this.logger.warn("Backblaze B2 client not initialized. Skipping connection test.");
      return;
    }

    try {
      // Validate credentials on startup
      await this.s3Client.config.credentials();
      this.logger.log("Successfully connected to Backblaze B2");
    } catch (error: unknown) {
      const err = error as Error;
      this.logger.error("Failed to connect to Backblaze B2:", err.message);
      throw error;
    }
  }

  async uploadFile(file: FastifyFileUpload, path: string): Promise<{ url: string; size: number }> {
    if (!this.s3Client) {
      throw new Error("Backblaze B2 client not initialized. File upload is not available.");
    }

    try {
      const timestamp = Date.now();
      const key = `${path}/${timestamp}-${file.originalname}`;

      const command = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: key,
        Body: file.buffer, // Use buffer directly
        ContentType: file.mimetype,
        // Set Cache-Control for better performance
        CacheControl: "max-age=31536000"
      });

      await this.s3Client.send(command);
      // For Backblaze B2, use the proper endpoint format
      const url = `https://${this.endpoint}/file/${this.bucketName}/${key}`;
      return { url, size: file.size };
    } catch (error: unknown) {
      const err = error as Error;
      this.logger.error("Failed to upload file:", err.message);
      throw error;
    }
  }

  async deleteFile(key: string): Promise<void> {
    if (!this.s3Client) {
      throw new Error("Backblaze B2 client not initialized. File deletion is not available.");
    }

    try {
      const command = new DeleteObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      });

      await this.s3Client.send(command);
    } catch (error: unknown) {
      const err = error as Error;
      this.logger.error("Failed to delete file:", err.message);
      throw error;
    }
  }
}
