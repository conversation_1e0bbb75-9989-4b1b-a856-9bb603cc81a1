import { InputType, Field } from '@nestjs/graphql';
import { TicketPriority } from '@prisma/client';
import { IsString, IsEnum, IsUUID, IsArray, IsOptional } from 'class-validator';

@InputType()
export class CreateTicketInput {
  @Field()
  @IsString()
  subject: string;

  @Field()
  @IsString()
  description: string;

  @Field(() => TicketPriority)
  @IsEnum(TicketPriority)
  priority: TicketPriority;

  @Field()
  @IsUUID()
  categoryId: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  accountId?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  partnerId?: string;

  @Field(() => [String], { nullable: true })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  assignedTo?: string[];
}
