generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum CategoryType {
  internal
  partner
  software_partner
}

model Category {
  id               String         @id @default(uuid())
  name             String         @unique
  description      String?
  type             CategoryType   @default(internal)
  autoAssignTo     String[]      // Array of IDs (partnerOrgId, softwarePartnerId, teamId)
  timeoutMinutes   Int           @default(60)  // Default 1 hour
  escalateTo       String?       // teamId, supportAdminId, or partnerOrgId
  createdAt        DateTime      @default(now())
  updatedAt        DateTime      @updatedAt
  deletedAt        DateTime?     // Soft delete timestamp
  tickets          Ticket[]

  @@index([deletedAt])
}

model Ticket {
  id            String         @id @default(uuid())
  subject       String
  description   String
  priority      TicketPriority
  status        TicketStatus   @default(OPEN)
  categoryId    String
  accountId     String?
  partnerId     String?
  assignedTo    String[]
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  deletedAt     DateTime?      // Soft delete timestamp
  createdBy     String
  lastUpdatedBy String
  sub           String?        // JWT subject for user identification
  // User data fields fetched from auth API to avoid repeated external calls
  firstName     String?        // User's first name from /auth/users/{id}
  lastName      String?        // User's last name from /auth/users/{id}
  email         String?        // User's email from /auth/users/{id}
  // Partner-related fields for future use
  partnerUserId String?        // Maps to partner_user from JWT
  partnerRole   String?        // Maps to partner_role from JWT
  partnerOrgId  String?        // Maps to partner_id from JWT
  entSet        String?        // Maps to ent_set from JWT for role/permission determination
  comments      Comment[]
  files         SupportFile[]
  category      Category       @relation(fields: [categoryId], references: [id])

  @@index([deletedAt])
  @@index([partnerId, deletedAt])
  @@index([accountId, deletedAt])
  @@index([sub])
}

model Comment {
  id        String        @id @default(uuid())
  message   String
  authorId  String
  ticketId  String
  createdAt DateTime      @default(now())
  updatedAt DateTime      @updatedAt
  deletedAt DateTime?     // Soft delete timestamp
  // User data fields fetched from auth API to avoid repeated external calls
  firstName     String?        // User's first name from /auth/users/{id}
  lastName      String?        // User's last name from /auth/users/{id}
  email         String?        // User's email from /auth/users/{id}
  // Partner-related fields for future use
  partnerUserId String?        // Maps to partner_user from JWT
  partnerRole   String?        // Maps to partner_role from JWT
  partnerOrgId  String?        // Maps to partner_id from JWT
  entSet        String?        // Maps to ent_set from JWT for role/permission determination
  // User data fields for display purposes
  authorName String?      // Author's full name
  authorEmail String?     // Author's email
  ticket    Ticket        @relation(fields: [ticketId], references: [id], onDelete: Cascade)
  files     SupportFile[]

  @@index([deletedAt])
  @@index([ticketId, deletedAt])
}

model SupportFile {
  id         String   @id @default(uuid())
  filename   String
  fileSize   Int
  fileUrl    String
  mimeType   String
  uploadedBy String
  ticketId   String?
  commentId  String?
  uploadedAt DateTime @default(now())
  comment    Comment? @relation(fields: [commentId], references: [id], onDelete: Cascade)
  ticket     Ticket?  @relation(fields: [ticketId], references: [id], onDelete: Cascade)
}

enum TicketPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum TicketStatus {
  OPEN
  IN_PROGRESS
  RESOLVED
  CLOSED
}

enum CustomValueType {
  NUMBER
  STRING
  JSON
  BOOLEAN
}

model CustomValue {
  id            String         @id @default(uuid())
  key           String         @unique   // Unique identifier like "DEFAULT_TIMEOUT_MINUTES" or "WELCOME_MESSAGE"
  name          String                   // Display name like "Default Timeout Minutes" or "Welcome Message"
  description   String?                  // Description of what this value is used for
  value         String                   // Stored as string, can be parsed based on type
  type          CustomValueType          // Type helps in validation and parsing
  category      String                   // For grouping: "TICKET", "NOTIFICATION", "SYSTEM", etc.
  isSystem      Boolean        @default(false)  // If true, cannot be deleted, only modified
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  createdBy     String                   // User who created this value
  lastUpdatedBy String                   // User who last updated this value
}

// Example usage via comments:
// Timeout settings:
// - DEFAULT_TIMEOUT_MINUTES: "60" (NUMBER)
// - ESCALATION_LEVELS: "[{\"timeoutMinutes\": 30, \"escalateTo\": \"TEAM_LEAD\"}, ...]" (JSON)
// 
// Message templates:
// - TICKET_CREATED_TEMPLATE: "New ticket {{ticketId}} has been created..." (STRING)
// - AUTO_CLOSE_MESSAGE: "This ticket will be closed due to inactivity..." (STRING)
// 
// System settings:
// - ALLOW_GUEST_TICKETS: "true" (BOOLEAN)
// - MAX_FILE_SIZE_MB: "10" (NUMBER)
// - ALLOWED_FILE_TYPES: "[\"pdf\", \"docx\", \"png\"]" (JSON)
