import { ObjectType, Field, ID } from '@nestjs/graphql';
import { TicketPriority } from '@prisma/client';
import { Category } from '../category/category.model';
import { Comment } from '../comment/comment.model';
import { SupportFile } from '../backblaze/support-file.model';

@ObjectType('Ticket')
export class TicketModel {
  @Field(() => ID)
  id: string;

  @Field()
  subject: string;

  @Field()
  description: string;

  @Field()
  status: string;

  @Field(() => TicketPriority)
  priority: TicketPriority;

  @Field({ nullable: true })
  accountId?: string;

  @Field({ nullable: true })
  partnerId?: string;

  @Field(() => [String], { nullable: true })
  assignedTo?: string[];

  @Field()
  createdBy: string;

  @Field()
  lastUpdatedBy: string;

  @Field({ nullable: true, description: 'First name of the user who created the ticket (from auth service)' })
  firstName?: string;

  @Field({ nullable: true, description: 'Last name of the user who created the ticket (from auth service)' })
  lastName?: string;

  @Field({ nullable: true, description: 'Email of the user who created the ticket (from auth service)' })
  email?: string;

  @Field({ nullable: true, description: 'Partner user ID (JWT sub) of the ticket creator' })
  partnerUserId?: string;

  @Field({ nullable: true, description: 'Partner role/scope of the ticket creator (e.g., partner:support:user)' })
  partnerRole?: string;

  @Field({ nullable: true, description: 'Partner organization ID (public_uid from partner service)' })
  partnerOrgId?: string;

  @Field({ nullable: true, description: 'Entity set type (partner/account) from JWT' })
  entSet?: string;

  @Field()
  createdAt: Date;

  @Field()
  updatedAt: Date;

  @Field(() => Category)
  category: Category;

  @Field(() => [Comment], { nullable: true })
  comments?: Comment[];

  @Field(() => [SupportFile], { nullable: true })
  files?: SupportFile[];
}
