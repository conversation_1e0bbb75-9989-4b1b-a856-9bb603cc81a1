import {
  Controller,
  Post,
  Get,
  Delete,
  UseGuards,
  Request,
  Body,
  Query,
  Param,
  NotFoundException,
  BadRequestException,
  InternalServerErrorException,
  Req
} from "@nestjs/common";
import {
  ApiBearerAuth,
  ApiConsumes,
  ApiOperation,
  ApiTags,
  ApiResponse,
  ApiQuery,
  ApiBody
} from "@nestjs/swagger";
import { SupportFile } from "@prisma/client";
import { ApiGuard } from "../auth/src/guards";
import { ScopeGuard, SupportAccess, RequirePermissions } from "../auth/src/guards/scope.guard";
import { AuthenticatedRequest } from "../auth/src/types";
import { ScopeContext } from "../auth/src/types/scope.types";
import { multerConfig } from "./config/multer.config";
import { FileUploadDto, FileResponseDto } from "./dto/file-upload.dto";
import { FileService } from "./file.service";
import { FastifyRequest } from "fastify";

// Define a type for Fastify file upload
export interface FastifyFileUpload {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: string;
  buffer: Buffer;
  size: number;
}

@ApiTags("Files")
@Controller("files")
@UseGuards(ApiGuard, ScopeGuard)
@SupportAccess()
@ApiBearerAuth()
export class FileController {
  constructor(private readonly fileService: FileService) {}

  @Post("upload")
  @RequirePermissions('canUploadFiles')
  @ApiOperation({
    summary: "Upload a file",
    description: "Upload a file and associate it with either a ticket or comment. Exactly one of ticketId or commentId must be provided, but not both."
  })
  @ApiConsumes("multipart/form-data")
  @ApiBody({
    schema: {
      type: "object",
      properties: {
        file: {
          type: "string",
          format: "binary",
          description: "The file to upload"
        },
        ticketId: {
          type: "string",
          description: "ID of the ticket to associate the file with. Either ticketId or commentId must be provided, but not both.",
          example: "200ee76d-fa54-4f5d-b43a-82a56c0b8084"
        },
        commentId: {
          type: "string",
          description: "ID of the comment to associate the file with. Either ticketId or commentId must be provided, but not both.",
          example: "e3105fa6-7a81-4aaa-b0e2-50d646102dee"
        },
      },
      required: ["file"],
    },
  })
  @ApiResponse({
    status: 201,
    description: "File uploaded successfully",
    type: FileResponseDto,
    examples: {
      ticketFile: {
        summary: "File associated with ticket",
        value: {
          id: "f47ac10b-58cc-4372-a567-0e02b2c3d479",
          filename: "document.pdf",
          fileSize: 1024000,
          fileUrl: "https://f005.backblazeb2.com/file/bucket-name/tickets/ticket-id/1726567890123-document.pdf",
          mimeType: "application/pdf",
          uploadedBy: "user-123",
          ticketId: "200ee76d-fa54-4f5d-b43a-82a56c0b8084",
          commentId: null,
          uploadedAt: "2025-09-17T10:30:00.000Z"
        }
      },
      commentFile: {
        summary: "File associated with comment",
        value: {
          id: "a23bc45d-67ef-4890-a123-456789abcdef",
          filename: "screenshot.png",
          fileSize: 512000,
          fileUrl: "https://f005.backblazeb2.com/file/bucket-name/comments/comment-id/1726567890456-screenshot.png",
          mimeType: "image/png",
          uploadedBy: "user-123",
          ticketId: null,
          commentId: "e3105fa6-7a81-4aaa-b0e2-50d646102dee",
          uploadedAt: "2025-09-17T10:35:00.000Z"
        }
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: "Bad Request - Invalid file association",
    examples: {
      bothProvided: {
        summary: "Both ticketId and commentId provided",
        value: {
          message: "Cannot associate file with both ticket and comment",
          error: "Bad Request",
          statusCode: 400
        }
      },
      neitherProvided: {
        summary: "Neither ticketId nor commentId provided",
        value: {
          message: "File must be associated with either a ticket or comment",
          error: "Bad Request",
          statusCode: 400
        }
      },
      noPermission: {
        summary: "No permission to upload to ticket/comment",
        value: {
          message: "You don't have permission to upload files to this ticket",
          error: "Bad Request",
          statusCode: 400
        }
      }
    }
  })
  async uploadFile(
    @Req() req: FastifyRequest & { scopeContext: ScopeContext }
  ): Promise<SupportFile> {
    try {
      // Handle multipart form data with Fastify
      const parts = req.parts();
      let fileObj: any = null;
      let ticketIdValue: string | undefined = undefined;
      let commentIdValue: string | undefined = undefined;

      // Process all parts of the multipart form
      for await (const part of parts) {
        console.log('DEBUG - Processing part:', part.fieldname, 'type:', part.type);

        if (part.type === 'file') {
          // This is the file part
          const buffer = await part.toBuffer();
          fileObj = {
            fieldname: part.fieldname,
            originalname: part.filename,
            encoding: part.encoding,
            mimetype: part.mimetype,
            buffer,
            size: buffer.length,
          };
          console.log('DEBUG - File part processed:', part.filename);
        } else {
          // This is a form field
          const value = part.value as string;
          console.log('DEBUG - Field part:', part.fieldname, '=', value, 'type:', typeof value);

          if (part.fieldname === 'ticketId') {
            ticketIdValue = value;
          } else if (part.fieldname === 'commentId') {
            commentIdValue = value;
          }
        }
      }

      if (!fileObj) {
        throw new BadRequestException("File is required");
      }

      console.log('DEBUG - Final values:');
      console.log('DEBUG - ticketIdValue:', ticketIdValue, 'type:', typeof ticketIdValue);
      console.log('DEBUG - commentIdValue:', commentIdValue, 'type:', typeof commentIdValue);

      // Validate file association before processing
      this.validateFileAssociation(ticketIdValue, commentIdValue);

      return await this.fileService.uploadFileWithScope(
        fileObj,
        req.scopeContext,
        ticketIdValue,
        commentIdValue
      );
    } catch (error) {
      console.log('DEBUG - Error in uploadFile:', error);
      if (error instanceof BadRequestException || error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException("An error occurred while uploading the file");
    }
  }

  /**
   * Validate file association parameters in controller
   */
  private validateFileAssociation(ticketId?: string, commentId?: string): void {
    // Helper function to check if a value is considered "empty"
    const isEmpty = (val: any): boolean => {
      return val === null || val === undefined || val === '' || (typeof val === 'string' && val.trim() === '');
    };

    console.log('DEBUG - Validation input - ticketId:', ticketId, 'type:', typeof ticketId);
    console.log('DEBUG - Validation input - commentId:', commentId, 'type:', typeof commentId);

    const hasTicketId = !isEmpty(ticketId);
    const hasCommentId = !isEmpty(commentId);

    console.log('DEBUG - isEmpty(ticketId):', isEmpty(ticketId), 'hasTicketId:', hasTicketId);
    console.log('DEBUG - isEmpty(commentId):', isEmpty(commentId), 'hasCommentId:', hasCommentId);

    // Exactly one should be provided (XOR logic)
    if (!hasTicketId && !hasCommentId) {
      console.log('DEBUG - Validation failed: neither provided');
      throw new BadRequestException("File must be associated with either a ticket or comment");
    }

    if (hasTicketId && hasCommentId) {
      console.log('DEBUG - Validation failed: both provided');
      throw new BadRequestException("Cannot associate file with both ticket and comment");
    }

    console.log('DEBUG - Validation passed');
  }

  @Get()
  @RequirePermissions('canViewFiles')
  @ApiOperation({
    summary: "Get files for a ticket or comment",
    description: "Retrieve files associated with a specific ticket or comment. You can filter by either ticketId or commentId, but not both. If neither is provided, returns all files the user has access to."
  })
  @ApiQuery({
    name: "ticketId",
    required: false,
    description: "Filter files by ticket ID. Cannot be used together with commentId.",
    example: "200ee76d-fa54-4f5d-b43a-82a56c0b8084"
  })
  @ApiQuery({
    name: "commentId",
    required: false,
    description: "Filter files by comment ID. Cannot be used together with ticketId.",
    example: "e3105fa6-7a81-4aaa-b0e2-50d646102dee"
  })
  @ApiResponse({
    status: 200,
    description: "List of files",
    type: [FileResponseDto],
    examples: {
      ticketFiles: {
        summary: "Files for a specific ticket",
        value: [
          {
            id: "f47ac10b-58cc-4372-a567-0e02b2c3d479",
            filename: "document.pdf",
            fileSize: 1024000,
            fileUrl: "https://f005.backblazeb2.com/file/bucket-name/tickets/ticket-id/1726567890123-document.pdf",
            mimeType: "application/pdf",
            uploadedBy: "user-123",
            ticketId: "200ee76d-fa54-4f5d-b43a-82a56c0b8084",
            commentId: null,
            uploadedAt: "2025-09-17T10:30:00.000Z"
          }
        ]
      },
      commentFiles: {
        summary: "Files for a specific comment",
        value: [
          {
            id: "a23bc45d-67ef-4890-a123-456789abcdef",
            filename: "screenshot.png",
            fileSize: 512000,
            fileUrl: "https://f005.backblazeb2.com/file/bucket-name/comments/comment-id/1726567890456-screenshot.png",
            mimeType: "image/png",
            uploadedBy: "user-123",
            ticketId: null,
            commentId: "e3105fa6-7a81-4aaa-b0e2-50d646102dee",
            uploadedAt: "2025-09-17T10:35:00.000Z"
          }
        ]
      },
      allFiles: {
        summary: "All accessible files (no filter)",
        value: [
          {
            id: "f47ac10b-58cc-4372-a567-0e02b2c3d479",
            filename: "document.pdf",
            fileSize: 1024000,
            fileUrl: "https://f005.backblazeb2.com/file/bucket-name/tickets/ticket-id/1726567890123-document.pdf",
            mimeType: "application/pdf",
            uploadedBy: "user-123",
            ticketId: "200ee76d-fa54-4f5d-b43a-82a56c0b8084",
            commentId: null,
            uploadedAt: "2025-09-17T10:30:00.000Z"
          },
          {
            id: "a23bc45d-67ef-4890-a123-456789abcdef",
            filename: "screenshot.png",
            fileSize: 512000,
            fileUrl: "https://f005.backblazeb2.com/file/bucket-name/comments/comment-id/1726567890456-screenshot.png",
            mimeType: "image/png",
            uploadedBy: "user-123",
            ticketId: null,
            commentId: "e3105fa6-7a81-4aaa-b0e2-50d646102dee",
            uploadedAt: "2025-09-17T10:35:00.000Z"
          }
        ]
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: "Bad Request - Invalid query parameters",
    example: {
      message: "Cannot filter by both ticketId and commentId. Please provide only one.",
      error: "Bad Request",
      statusCode: 400
    }
  })
  async getFiles(
    @Req() req: FastifyRequest & { scopeContext: ScopeContext },
    @Query("ticketId") ticketId?: string,
    @Query("commentId") commentId?: string
  ): Promise<SupportFile[]> {
    try {
      // Helper function to check if a value is considered "empty"
      const isEmpty = (val: any): boolean => {
        return val === null || val === undefined || val === '' || (typeof val === 'string' && val.trim() === '');
      };

      // Validate that both ticketId and commentId are not provided together
      const hasTicketId = !isEmpty(ticketId);
      const hasCommentId = !isEmpty(commentId);

      if (hasTicketId && hasCommentId) {
        throw new BadRequestException("Cannot filter by both ticketId and commentId. Please provide only one.");
      }

      return await this.fileService.getFilesWithScope(req.scopeContext, ticketId, commentId);
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException("An error occurred while fetching files");
    }
  }

  @Get(":id")
  @RequirePermissions('canViewFiles')
  @ApiOperation({ summary: "Get a file by ID" })
  @ApiResponse({
    status: 200,
    description: "File details",
    type: FileResponseDto,
    example: {
      id: "f47ac10b-58cc-4372-a567-0e02b2c3d479",
      filename: "document.pdf",
      fileSize: 1024000,
      fileUrl: "https://f005.backblazeb2.com/file/bucket-name/tickets/ticket-id/1726567890123-document.pdf",
      mimeType: "application/pdf",
      uploadedBy: "user-123",
      ticketId: "200ee76d-fa54-4f5d-b43a-82a56c0b8084",
      commentId: null,
      uploadedAt: "2025-09-17T10:30:00.000Z"
    }
  })
  @ApiResponse({ status: 404, description: "File not found" })
  async getFile(
    @Req() req: FastifyRequest & { scopeContext: ScopeContext },
    @Param("id") id: string
  ): Promise<SupportFile> {
    try {
      return await this.fileService.getFileByIdWithScope(id, req.scopeContext);
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException("An error occurred while fetching the file");
    }
  }

  @Delete(":id")
  @RequirePermissions('canDeleteFiles')
  @ApiOperation({ summary: "Delete a file" })
  @ApiResponse({
    status: 200,
    description: "File deleted successfully",
    type: FileResponseDto,
    example: {
      id: "f47ac10b-58cc-4372-a567-0e02b2c3d479",
      filename: "document.pdf",
      fileSize: 1024000,
      fileUrl: "https://f005.backblazeb2.com/file/bucket-name/tickets/ticket-id/1726567890123-document.pdf",
      mimeType: "application/pdf",
      uploadedBy: "user-123",
      ticketId: "200ee76d-fa54-4f5d-b43a-82a56c0b8084",
      commentId: null,
      uploadedAt: "2025-09-17T10:30:00.000Z"
    }
  })
  @ApiResponse({ status: 404, description: "File not found" })
  async deleteFile(
    @Req() req: FastifyRequest & { scopeContext: ScopeContext },
    @Param("id") id: string
  ): Promise<SupportFile> {
    try {
      return await this.fileService.deleteFileWithScope(id, req.scopeContext);
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException("An error occurred while deleting the file");
    }
  }
}

