import { Module, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '../../../config/config.module';
import { AuthSharedService } from './auth.service';
import { UserDataService } from './user-data.service';
import { ScopeResolutionService } from './scope-resolution.service';
import { ScopeFilterService } from './scope-filter.service';
import { AuthSharedGuard } from './auth.guard';
import { AuthSharedMiddleware } from './auth.middleware';
import { AuthSharedController } from './auth.controller';
import { AuthResolver } from './auth.resolver';
import { ApiGuard, AdminGuard, GraphQLAuthGuard } from './guards';
import { ScopeGuard } from './guards/scope.guard';

@Module({
  imports: [ConfigModule, HttpModule],
  providers: [AuthSharedService, UserDataService, ScopeResolutionService, ScopeFilterService, AuthSharedGuard, AuthSharedMiddleware, ApiGuard, AdminGuard, GraphQLAuthGuard, ScopeGuard, AuthResolver],
  controllers: [AuthSharedController],
  exports: [AuthSharedService, UserDataService, ScopeResolutionService, ScopeFilterService, AuthSharedGuard, AuthSharedMiddleware, ApiGuard, AdminGuard, GraphQLAuthGuard, ScopeGuard, AuthResolver],
})
export class AuthModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(AuthSharedMiddleware)
      .forRoutes('*'); // Apply to all routes
  }
}

// Export everything for easy importing
export * from './auth.service';
export * from './user-data.service';
export * from './scope-resolution.service';
export * from './scope-filter.service';
export { AuthSharedGuard, Roles, Permissions, Public } from './auth.guard';
export * from './auth.middleware';
export * from './auth.controller';
export * from './auth.resolver';
export * from './guards';
export * from './guards/scope.guard';
export { getCurrentUser } from './decorators/user.decorator';
export * from './types/auth.types';
export * from './types/graphql.types';
export * from './types/scope.types';
export * from './types';
