import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, ValidateIf, ValidationArguments, registerDecorator, ValidationOptions } from 'class-validator';

/**
 * Custom validator to ensure exactly one of ticketId or commentId is provided
 */
export function IsExactlyOneOf(property: string, validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isExactlyOneOf',
      target: object.constructor,
      propertyName: propertyName,
      constraints: [property],
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          const [relatedPropertyName] = args.constraints;
          const relatedValue = (args.object as any)[relatedPropertyName];

          // Helper function to check if a value is considered "empty"
          const isEmpty = (val: any): boolean => {
            return val === null || val === undefined || val === '' || (typeof val === 'string' && val.trim() === '');
          };

          // Exactly one should be provided (not both, not neither)
          const hasCurrentValue = !isEmpty(value);
          const hasRelatedValue = !isEmpty(relatedValue);

          return hasCurrentValue !== hasRelatedValue; // XOR logic
        },
        defaultMessage(args: ValidationArguments) {
          const [relatedPropertyName] = args.constraints;
          return `Either ${args.property} or ${relatedPropertyName} must be provided, but not both`;
        },
      },
    });
  };
}

export class FileUploadDto {
  @ApiProperty({
    required: false,
    description: "The ID of the ticket to attach the file to. Either ticketId or commentId must be provided, but not both.",
    example: "200ee76d-fa54-4f5d-b43a-82a56c0b8084"
  })
  @IsString()
  @IsOptional()
  @IsExactlyOneOf('commentId', { message: 'File must be associated with either a ticket or comment, but not both' })
  ticketId?: string;

  @ApiProperty({
    required: false,
    description: "The ID of the comment to attach the file to. Either ticketId or commentId must be provided, but not both.",
    example: "e3105fa6-7a81-4aaa-b0e2-50d646102dee"
  })
  @IsString()
  @IsOptional()
  @IsExactlyOneOf('ticketId', { message: 'File must be associated with either a ticket or comment, but not both' })
  commentId?: string;
}

export class FileResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  filename: string;

  @ApiProperty()
  fileSize: number;

  @ApiProperty()
  fileUrl: string;

  @ApiProperty()
  mimeType: string;

  @ApiProperty()
  uploadedBy: string;

  @ApiProperty({ required: false, nullable: true })
  ticketId: string | null;

  @ApiProperty({ required: false, nullable: true })
  commentId: string | null;

  @ApiProperty()
  uploadedAt: Date;
}
