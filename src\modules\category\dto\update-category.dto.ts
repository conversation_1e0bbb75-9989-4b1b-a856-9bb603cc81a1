import { InputType, Field, Int, registerEnumType } from "@nestjs/graphql";
import { ApiProperty } from "@nestjs/swagger";
import { CategoryType } from "@prisma/client";
import { IsString, IsOptional, IsEnum, IsArray, IsNumber, Min, Max } from "class-validator";

registerEnumType(CategoryType, {
  name: "CategoryType",
  description: "The type of the category (internal, partner, software_partner)",
});

@InputType()
export class UpdateCategoryDto {
  @Field({ nullable: true })
  @ApiProperty({ description: "Category name", required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @Field({ nullable: true })
  @ApiProperty({ description: "Category description", required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @Field(() => CategoryType, { nullable: true })
  @ApiProperty({ description: "Category type", enum: CategoryType, required: false })
  @IsOptional()
  @IsEnum(CategoryType)
  type?: CategoryType;

  @Field({ nullable: true })
  @ApiProperty({ description: "ID to escalate to (teamId, supportAdminId, or partnerOrgId)", required: false })
  @IsOptional()
  @IsString()
  escalateTo?: string;

  @Field(() => [String], { nullable: "itemsAndList" })
  @ApiProperty({ description: "Auto-assign tickets to these IDs (partnerOrgId, softwarePartnerId, teamId)", type: [String], required: false })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  autoAssignTo?: string[];

  @Field(() => Int, { nullable: true })
  @ApiProperty({ description: "Timeout in minutes before escalation", minimum: 1, maximum: 10080, required: false })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(10080)
  timeoutMinutes?: number;
}
