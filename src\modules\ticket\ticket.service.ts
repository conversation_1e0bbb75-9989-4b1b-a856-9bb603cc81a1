import { Injectable, NotFoundException, BadRequestException, Logger } from "@nestjs/common";
import { Prisma, TicketPriority, TicketStatus } from "@prisma/client";
import { PrismaService } from "../../prisma/prisma.service";
import {
  TicketPayload,
  ticketSelect
} from "./types/ticket.types";
import { ScopeFilterService } from "../auth/src/scope-filter.service";
import { ScopeContext } from "../auth/src/types/scope.types";

export interface CreateTicketParams {
  subject: string;
  description: string;
  priority: TicketPriority;
  categoryId: string;
  accountId?: string;
  partnerId?: string;
  assignedTo?: string[];
  createdBy: string;
  lastUpdatedBy: string;
  sub?: string; // JWT subject for user identification
  // User data fields fetched from auth API
  firstName?: string;
  lastName?: string;
  email?: string;
  // Partner-related fields for future use
  partnerUserId?: string;
  partnerRole?: string;
  partnerOrgId?: string;
  entSet?: string;
}

@Injectable()
export class TicketService {
  private readonly logger = new Logger(TicketService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly scopeFilterService: ScopeFilterService
  ) {}

  async create(data: CreateTicketParams): Promise<TicketPayload> {
    console.log('TicketService.create received data:', data);

    const createInput: Prisma.TicketCreateInput = {
      subject: data.subject,
      description: data.description,
      priority: data.priority,
      category: {
        connect: { id: data.categoryId }
      },
      status: "OPEN",
      createdBy: data.createdBy,
      lastUpdatedBy: data.lastUpdatedBy,
      ...(data.sub && {
        sub: data.sub
      }),
      ...(data.accountId && {
        accountId: data.accountId
      }),
      ...(data.partnerId && {
        partnerId: data.partnerId
      }),
      ...(data.assignedTo?.length && {
        assignedTo: data.assignedTo
      }),
      // Include user data fields fetched from auth API
      ...(data.firstName && {
        firstName: data.firstName
      }),
      ...(data.lastName && {
        lastName: data.lastName
      }),
      ...(data.email && {
        email: data.email
      }),
      // Include partner-related fields for future use
      ...(data.partnerUserId && {
        partnerUserId: data.partnerUserId
      }),
      ...(data.partnerRole && {
        partnerRole: data.partnerRole
      }),
      ...(data.partnerOrgId && {
        partnerOrgId: data.partnerOrgId
      }),
      ...(data.entSet && {
        entSet: data.entSet
      })
    };

    return this.prisma.ticket.create({
      data: createInput,
      select: ticketSelect
    });
  }

  async findUnique(where: Prisma.TicketWhereUniqueInput): Promise<TicketPayload | null> {
    return this.prisma.ticket.findUnique({
      where,
      select: ticketSelect
    });
  }

  async findMany(params: {
    skip?: number;
    take?: number;
    where?: Prisma.TicketWhereInput;
    orderBy?: Prisma.TicketOrderByWithRelationInput | Prisma.TicketOrderByWithRelationInput[];
  } = {}): Promise<TicketPayload[]> {
    const { skip, take, where, orderBy } = params;
    return this.prisma.ticket.findMany({
      skip,
      take,
      where,
      orderBy: orderBy || { createdAt: "desc" },
      select: ticketSelect
    });
  }

  async update(
    where: Prisma.TicketWhereUniqueInput,
    data: Prisma.TicketUpdateInput
  ): Promise<TicketPayload> {
    return this.prisma.ticket.update({
      where,
      data,
      select: ticketSelect
    });
  }

  async delete(where: Prisma.TicketWhereUniqueInput): Promise<TicketPayload> {
    return this.prisma.ticket.delete({
      where,
      select: ticketSelect
    });
  }

  // Scope-aware methods

  /**
   * Find tickets with scope-based filtering
   */
  async findManyWithScope(
    scopeContext: ScopeContext,
    params: {
      skip?: number;
      take?: number;
      where?: Prisma.TicketWhereInput;
      orderBy?: Prisma.TicketOrderByWithRelationInput | Prisma.TicketOrderByWithRelationInput[];
    } = {}
  ): Promise<TicketPayload[]> {
    const { skip, take, where = {}, orderBy } = params;

    // Apply scope-based filtering
    const scopedWhere = this.scopeFilterService.applyTicketFilters(scopeContext, where);

    this.logger.log(`🔍 [TICKET SERVICE] Finding tickets with scope filtering for user ${scopeContext.userId}`);

    return this.prisma.ticket.findMany({
      skip,
      take,
      where: scopedWhere,
      orderBy: orderBy || { createdAt: "desc" },
      select: ticketSelect
    });
  }

  /**
   * Find unique ticket with scope validation
   */
  async findUniqueWithScope(
    scopeContext: ScopeContext,
    where: Prisma.TicketWhereUniqueInput
  ): Promise<TicketPayload | null> {
    const ticket = await this.prisma.ticket.findUnique({
      where,
      select: ticketSelect
    });

    if (!ticket) {
      return null;
    }

    // Check if user can access this ticket
    if (!this.scopeFilterService.canAccessTicket(scopeContext, ticket)) {
      this.scopeFilterService.logAccessDecision(scopeContext, 'ticket', 'view', false);
      throw new NotFoundException('Ticket not found or access denied');
    }

    this.scopeFilterService.logAccessDecision(scopeContext, 'ticket', 'view', true);
    return ticket;
  }

  /**
   * Update ticket with scope validation
   */
  async updateWithScope(
    scopeContext: ScopeContext,
    where: Prisma.TicketWhereUniqueInput,
    data: Prisma.TicketUpdateInput
  ): Promise<TicketPayload> {
    // First check if ticket exists and user can access it
    const existingTicket = await this.findUniqueWithScope(scopeContext, where);
    if (!existingTicket) {
      throw new NotFoundException('Ticket not found or access denied');
    }

    // Check if user can modify this ticket
    if (!this.scopeFilterService.canModifyTicket(scopeContext, existingTicket)) {
      this.scopeFilterService.logAccessDecision(scopeContext, 'ticket', 'update', false);
      throw new BadRequestException('Insufficient permissions to update this ticket');
    }

    this.scopeFilterService.logAccessDecision(scopeContext, 'ticket', 'update', true);

    return this.prisma.ticket.update({
      where,
      data,
      select: ticketSelect
    });
  }

  /**
   * Soft delete ticket with scope validation
   */
  async softDeleteWithScope(
    scopeContext: ScopeContext,
    where: Prisma.TicketWhereUniqueInput
  ): Promise<TicketPayload> {
    // First check if ticket exists and user can access it
    const existingTicket = await this.findUniqueWithScope(scopeContext, where);
    if (!existingTicket) {
      throw new NotFoundException('Ticket not found or access denied');
    }

    // Check if user can delete this ticket
    if (!this.scopeFilterService.canDeleteTicket(scopeContext, existingTicket)) {
      this.scopeFilterService.logAccessDecision(scopeContext, 'ticket', 'delete', false);
      throw new BadRequestException('Insufficient permissions to delete this ticket');
    }

    this.scopeFilterService.logAccessDecision(scopeContext, 'ticket', 'delete', true);

    return this.prisma.ticket.update({
      where,
      data: {
        deletedAt: new Date(),
        lastUpdatedBy: scopeContext.userId
      },
      select: ticketSelect
    });
  }

  /**
   * Create ticket with scope-based data preparation
   */
  async createWithScope(
    scopeContext: ScopeContext,
    data: CreateTicketParams
  ): Promise<TicketPayload> {
    // Prepare data with scope context
    const scopedData = this.scopeFilterService.getTicketCreationData(scopeContext, data);

    this.logger.log(`📝 [TICKET SERVICE] Creating ticket with scope context for user ${scopeContext.userId}`);

    return this.create(scopedData);
  }
}
