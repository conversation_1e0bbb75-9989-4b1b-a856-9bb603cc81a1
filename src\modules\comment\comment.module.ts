import { Module, forwardRef } from '@nestjs/common';
import { CommentController } from './comment.controller';
import { CommentService } from './comment.service';
import { CommentResolver } from './comment.resolver';
import { AuthModule } from '../auth/src/auth.module';
import { PrismaModule } from '../../prisma/prisma.module';
import { TicketModule } from '../ticket/ticket.module';

@Module({
  imports: [AuthModule, PrismaModule, forwardRef(() => TicketModule)],
  controllers: [CommentController],
  providers: [CommentService, CommentResolver],
  exports: [CommentService],
})
export class CommentModule {}
