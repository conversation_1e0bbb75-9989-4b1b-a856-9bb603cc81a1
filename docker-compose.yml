version: '3.8'

services:
  # Support Backend API Service
  support-backend-api:
    build: .
    container_name: support-backend-api
    ports:
      - '3040:3040'
    env_file:
      - .env.staging
    environment:
      - NODE_ENV=staging
      - API_PORT=3040
      - DATABASE_URL=******************************************************/postgres?schema=public
      - ALLOWED_ORIGINS=https://ng-support-dev.dev1.ngnair.com,https://ng-support-fe-dev.dev1.ngnair.com,https://ng-support-admin-dev.dev1.ngnair.com,https://ng-auth-dev.dev1.ngnair.com
      - AUTH_SERVICE_URL=https://ng-auth-dev.dev1.ngnair.com
      - AUTH_JWKS_URL=https://ng-auth-dev.dev1.ngnair.com/api/v1/jwks
      - ACCESS_TOKEN_ENCRYPTION_KEY=b7e2c1a4d8f3e6b9c2f7a1e4d3b8c6f2e1a7b4c9d6e3f8a2b5c4d7e1f6a3b2c8
      - FRONTEND_URL=https://ng-support-fe-dev.dev1.ngnair.com
      - ADMIN_URL=https://ng-support-admin-dev.dev1.ngnair.com
      - AUTH_URL=https://ng-auth-dev.dev1.ngnair.com
      - LISTENING_PORT=3040
      - PORT=3040
      - DOCKER_ENV=true
      - SOURCE_IP=0.0.0.0
      - COOKIE_SECRET=staging-secret-key-change-in-production
    depends_on:
      support-backend-db:
        condition: service_healthy
    networks:
      - backend-network
    healthcheck:
      test: ["CMD-SHELL", "wget -q --spider http://localhost:3040/api/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # PostgreSQL Database for Backend
  support-backend-db:
    image: postgres:15-alpine
    container_name: support-backend-db
    restart: always
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: postgres
    ports:
      - '5432:5432'
    volumes:
      - backend-pgdata:/var/lib/postgresql/data
      - ./z-init.d:/docker-entrypoint-initdb.d
    networks:
      - backend-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 5

networks:
  backend-network:
    driver: bridge
    name: support-backend-network

volumes:
  backend-pgdata:
    name: support-backend-pgdata
