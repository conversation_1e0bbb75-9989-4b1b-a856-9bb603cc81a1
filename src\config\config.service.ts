import * as dotenv from "dotenv";
import * as fs from "fs";
import { Injectable, Logger } from "@nestjs/common";
import { Config } from "./config.types";

@Injectable()
export class ConfigService {
  private readonly envConfig: Partial<Config>;
  private readonly logger = new Logger(ConfigService.name);

  constructor() {
    const environment = process.env.NODE_ENV || "development";
    this.logger.log(`Loading configuration for environment: ${environment}`);
    
    try {
      // Try environment-specific file first
      const envFilePath = `.env.${environment}`;
      const fallbackEnvFilePath = ".env";
      
      if (fs.existsSync(envFilePath)) {
        this.logger.log(`Loading configuration from ${envFilePath}`);
        this.envConfig = dotenv.parse(fs.readFileSync(envFilePath));
      } else if (fs.existsSync(fallbackEnvFilePath)) {
        this.logger.log(`Loading configuration from ${fallbackEnvFilePath}`);
        this.envConfig = dotenv.parse(fs.readFileSync(fallbackEnvFilePath));
      } else {
        this.logger.warn("No .env file found. Using process.env only.");
        this.envConfig = {};
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      this.logger.error(`Error loading configuration: ${errorMessage}`);
      this.envConfig = {};
    }
  }

  /**
   * Get a configuration value
   * @param key The configuration key to get
   * @param defaultValue Optional default value if key not found
   * @returns The configuration value or empty string if not found and no default provided
   */
  get<T extends keyof Config>(key: T, defaultValue?: Config[T]): Config[T] {
    const value = process.env[key] || this.envConfig[key] || defaultValue;
    return value as Config[T];
  }

  /**
   * Get a configuration value as a number
   * @param key The configuration key to get
   * @param defaultValue Optional default value if key not found
   * @returns The configuration value as a number or 0 if not found and no default provided
   */
  getNumber(key: keyof Config, defaultValue = 0): number {
    const value = this.get(key);
    if (value === undefined || value === "") {
      return defaultValue;
    }
    return Number(value);
  }

  /**
   * Get a configuration value as a boolean
   * @param key The configuration key to get
   * @param defaultValue Optional default value if key not found
   * @returns The configuration value as a boolean or false if not found and no default provided
   */
  getBoolean(key: keyof Config, defaultValue = false): boolean {
    const value = this.get(key);
    if (value === undefined || value === "") {
      return defaultValue;
    }
    return String(value).toLowerCase() === "true";
  }

  /**
   * Get CORS allowed origins from configuration
   * @returns Array of allowed origins
   */
  getAllowedOrigins(): string[] {
    const origins = this.get("ALLOWED_ORIGINS", "http://localhost:3000");
    return String(origins).split(",").map((origin: string) => origin.trim());
  }
}
